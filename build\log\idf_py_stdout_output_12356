[1/5] cmd.exe /C "cd /D D:\vscode\projects-lvgl\tusb_serial_device\build\esp-idf\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe D:/esp/v5.1.2/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app D:/vscode/projects-lvgl/tusb_serial_device/build/partition_table/partition-table.bin D:/vscode/projects-lvgl/tusb_serial_device/build/tusb_serial_device.bin"

tusb_serial_device.bin binary size 0x42760 bytes. Smallest app partition is 0x100000 bytes. 0xbd8a0 bytes (74%) free.

[2/5] Performing build step for 'bootloader'

[1/1] cmd.exe /C "cd /D D:\vscode\projects-lvgl\tusb_serial_device\build\bootloader\esp-idf\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe D:/esp/v5.1.2/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader/bootloader.bin"

Bootloader binary size 0x51c0 bytes. 0x2e40 bytes (36%) free.

[2/3] cmd.exe /C "cd /D D:\esp\v5.1.2\esp-idf\components\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\tools\cmake\3.24.0\bin\cmake.exe -D IDF_PATH=D:/esp/v5.1.2/esp-idf -D SERIAL_TOOL=D:/vscode/vsc-lvgl/v5.0.6/esp-idf/python_env/idf5.1_py3.11_env/Scripts/python.exe;;D:/esp/v5.1.2/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=D:/vscode/projects-lvgl/tusb_serial_device/build -P D:/esp/v5.1.2/esp-idf/components/esptool_py/run_serial_tool.cmake"

esptool.py --chip esp32s3 -p COM6 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 2MB 0x0 bootloader/bootloader.bin 0x10000 tusb_serial_device.bin 0x8000 partition_table/partition-table.bin

esptool.py v4.8.1

Serial port COM6



A fatal error occurred: Could not open COM6, the port is busy or doesn't exist.

(could not open port 'COM6': PermissionError(13, 'Access is denied.', None, 5))



Hint: Check if the port is not used by another task



FAILED: CMakeFiles/flash D:/vscode/projects-lvgl/tusb_serial_device/build/CMakeFiles/flash 

cmd.exe /C "cd /D D:\esp\v5.1.2\esp-idf\components\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\tools\cmake\3.24.0\bin\cmake.exe -D IDF_PATH=D:/esp/v5.1.2/esp-idf -D SERIAL_TOOL=D:/vscode/vsc-lvgl/v5.0.6/esp-idf/python_env/idf5.1_py3.11_env/Scripts/python.exe;;D:/esp/v5.1.2/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=D:/vscode/projects-lvgl/tusb_serial_device/build -P D:/esp/v5.1.2/esp-idf/components/esptool_py/run_serial_tool.cmake"

ninja: build stopped: subcommand failed.

