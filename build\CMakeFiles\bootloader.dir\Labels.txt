# Target labels
 bootloader
# Source files and their labels
D:/vscode/projects-lvgl/tusb_serial_device/build/CMakeFiles/bootloader
D:/vscode/projects-lvgl/tusb_serial_device/build/CMakeFiles/bootloader.rule
D:/vscode/projects-lvgl/tusb_serial_device/build/CMakeFiles/bootloader-complete.rule
D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule
D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule
D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule
D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule
D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule
D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule
D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule
