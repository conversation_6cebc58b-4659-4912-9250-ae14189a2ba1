/*
 * SPDX-FileCopyrightText: 2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */

#include <stdint.h>
#include <string.h>
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "tinyusb.h"
#include "tusb_cdc_acm.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "sdkconfig.h"

static const char *TAG = "example";
static uint8_t buf[CONFIG_TINYUSB_CDC_RX_BUFSIZE + 1];

// UART configuration for HLK-TX510 communication
#define HLK_UART_PORT_NUM      UART_NUM_1
#define HLK_UART_BAUD_RATE     115200
#define HLK_UART_TX_PIN        GPIO_NUM_17  // Available GPIO pin for TX
#define HLK_UART_RX_PIN        GPIO_NUM_18  // Available GPIO pin for RX
#define HLK_UART_RTS_PIN       UART_PIN_NO_CHANGE
#define HLK_UART_CTS_PIN       UART_PIN_NO_CHANGE

#define HLK_UART_BUF_SIZE      1024
#define HLK_CMD_SIZE           9
#define HLK_RESPONSE_MAX_SIZE  256

// Queue for communication between USB CDC and UART
static QueueHandle_t hlk_cmd_queue;
static QueueHandle_t hlk_response_queue;

// Buffer for HLK-TX510 communication
static uint8_t hlk_tx_buffer[HLK_CMD_SIZE];
static uint8_t hlk_rx_buffer[HLK_RESPONSE_MAX_SIZE];

/**
 * Initialize UART for HLK-TX510 communication
 */
static esp_err_t hlk_uart_init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = HLK_UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    // Install UART driver
    esp_err_t ret = uart_driver_install(HLK_UART_PORT_NUM, HLK_UART_BUF_SIZE * 2, 0, 0, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(ret));
        return ret;
    }

    // Configure UART parameters
    ret = uart_param_config(HLK_UART_PORT_NUM, &uart_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure UART parameters: %s", esp_err_to_name(ret));
        return ret;
    }

    // Set UART pins
    ret = uart_set_pin(HLK_UART_PORT_NUM, HLK_UART_TX_PIN, HLK_UART_RX_PIN, HLK_UART_RTS_PIN, HLK_UART_CTS_PIN);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "HLK-TX510 UART initialized on pins TX:%d, RX:%d", HLK_UART_TX_PIN, HLK_UART_RX_PIN);
    return ESP_OK;
}

/**
 * Send 9-byte hex command to HLK-TX510
 */
static esp_err_t hlk_send_command(const uint8_t *cmd, size_t cmd_len)
{
    if (cmd_len != HLK_CMD_SIZE) {
        ESP_LOGE(TAG, "Invalid command size: %d, expected: %d", cmd_len, HLK_CMD_SIZE);
        return ESP_ERR_INVALID_ARG;
    }

    // Send command to HLK-TX510
    int bytes_written = uart_write_bytes(HLK_UART_PORT_NUM, cmd, cmd_len);
    if (bytes_written < 0) {
        ESP_LOGE(TAG, "Failed to send command to HLK-TX510");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Sent %d bytes to HLK-TX510:", bytes_written);
    ESP_LOG_BUFFER_HEX(TAG, cmd, cmd_len);

    return ESP_OK;
}

/**
 * Receive response from HLK-TX510
 */
static int hlk_receive_response(uint8_t *response, size_t max_len, TickType_t timeout_ms)
{
    // Wait for data with timeout
    size_t bytes_available = 0;
    esp_err_t ret = uart_get_buffered_data_len(HLK_UART_PORT_NUM, &bytes_available);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get buffered data length");
        return -1;
    }

    if (bytes_available == 0) {
        // Wait for data to arrive
        vTaskDelay(timeout_ms / portTICK_PERIOD_MS);
        ret = uart_get_buffered_data_len(HLK_UART_PORT_NUM, &bytes_available);
        if (ret != ESP_OK || bytes_available == 0) {
            ESP_LOGW(TAG, "No response received from HLK-TX510");
            return 0;
        }
    }

    // Read available data
    size_t bytes_to_read = (bytes_available > max_len) ? max_len : bytes_available;
    int bytes_read = uart_read_bytes(HLK_UART_PORT_NUM, response, bytes_to_read, timeout_ms / portTICK_PERIOD_MS);

    if (bytes_read > 0) {
        ESP_LOGI(TAG, "Received %d bytes from HLK-TX510:", bytes_read);
        ESP_LOG_BUFFER_HEX(TAG, response, bytes_read);
    }

    return bytes_read;
}

/**
 * Task to handle HLK-TX510 communication
 */
static void hlk_communication_task(void *pvParameters)
{
    uint8_t cmd_buffer[HLK_CMD_SIZE];
    uint8_t response_buffer[HLK_RESPONSE_MAX_SIZE];

    ESP_LOGI(TAG, "HLK-TX510 communication task started");

    while (1) {
        // Wait for command from USB CDC
        if (xQueueReceive(hlk_cmd_queue, cmd_buffer, portMAX_DELAY) == pdTRUE) {
            ESP_LOGI(TAG, "Processing command for HLK-TX510");

            // Send command to HLK-TX510
            esp_err_t ret = hlk_send_command(cmd_buffer, HLK_CMD_SIZE);
            if (ret == ESP_OK) {
                // Wait for response
                int response_len = hlk_receive_response(response_buffer, HLK_RESPONSE_MAX_SIZE, pdMS_TO_TICKS(1000));
                if (response_len > 0) {
                    // Send response back to USB CDC via queue
                    if (xQueueSend(hlk_response_queue, response_buffer, pdMS_TO_TICKS(100)) != pdTRUE) {
                        ESP_LOGW(TAG, "Failed to queue response for USB CDC");
                    }
                } else {
                    ESP_LOGW(TAG, "No response received from HLK-TX510");
                    // Send empty response to indicate timeout
                    memset(response_buffer, 0, sizeof(response_buffer));
                    xQueueSend(hlk_response_queue, response_buffer, pdMS_TO_TICKS(100));
                }
            } else {
                ESP_LOGE(TAG, "Failed to send command to HLK-TX510");
            }
        }
    }
}

/**
 * Convert hex string to bytes
 */
static int hex_string_to_bytes(const char *hex_str, uint8_t *bytes, size_t max_bytes)
{
    size_t hex_len = strlen(hex_str);
    if (hex_len % 2 != 0) {
        return -1; // Invalid hex string length
    }

    size_t byte_count = hex_len / 2;
    if (byte_count > max_bytes) {
        return -1; // Too many bytes
    }

    for (size_t i = 0; i < byte_count; i++) {
        char hex_byte[3] = {hex_str[i*2], hex_str[i*2+1], '\0'};
        bytes[i] = (uint8_t)strtol(hex_byte, NULL, 16);
    }

    return byte_count;
}

/**
 * Convert bytes to hex string
 */
static void bytes_to_hex_string(const uint8_t *bytes, size_t byte_count, char *hex_str, size_t hex_str_size)
{
    if (hex_str_size < (byte_count * 2 + 1)) {
        return; // Not enough space
    }

    for (size_t i = 0; i < byte_count; i++) {
        sprintf(&hex_str[i*2], "%02X", bytes[i]);
    }
    hex_str[byte_count * 2] = '\0';
}

void tinyusb_cdc_rx_callback(int itf, cdcacm_event_t *event)
{
    /* initialization */
    size_t rx_size = 0;

    /* read */
    esp_err_t ret = tinyusb_cdcacm_read(itf, buf, CONFIG_TINYUSB_CDC_RX_BUFSIZE, &rx_size);
    if (ret == ESP_OK) {
        buf[rx_size] = '\0'; // Null terminate for string processing
        ESP_LOGI(TAG, "Data from USB CDC channel %d: %s", itf, buf);

        // Check if this is a 9-byte hex command (18 hex characters + possible newline/spaces)
        char *trimmed_data = (char *)buf;
        // Remove leading/trailing whitespace
        while (*trimmed_data == ' ' || *trimmed_data == '\t' || *trimmed_data == '\r' || *trimmed_data == '\n') {
            trimmed_data++;
        }

        size_t trimmed_len = strlen(trimmed_data);
        while (trimmed_len > 0 && (trimmed_data[trimmed_len-1] == ' ' || trimmed_data[trimmed_len-1] == '\t' ||
                                   trimmed_data[trimmed_len-1] == '\r' || trimmed_data[trimmed_len-1] == '\n')) {
            trimmed_data[--trimmed_len] = '\0';
        }

        // Check if it's exactly 18 hex characters (9 bytes)
        if (trimmed_len == 18) {
            uint8_t cmd_bytes[HLK_CMD_SIZE];
            int byte_count = hex_string_to_bytes(trimmed_data, cmd_bytes, HLK_CMD_SIZE);

            if (byte_count == HLK_CMD_SIZE) {
                ESP_LOGI(TAG, "Valid 9-byte hex command received, sending to HLK-TX510");

                // Send command to HLK communication task
                if (xQueueSend(hlk_cmd_queue, cmd_bytes, pdMS_TO_TICKS(100)) == pdTRUE) {
                    // Wait for response
                    uint8_t response_bytes[HLK_RESPONSE_MAX_SIZE];
                    if (xQueueReceive(hlk_response_queue, response_bytes, pdMS_TO_TICKS(2000)) == pdTRUE) {
                        // Convert response to hex string and send back via USB CDC
                        char response_hex[HLK_RESPONSE_MAX_SIZE * 2 + 10];
                        bytes_to_hex_string(response_bytes, strlen((char*)response_bytes), response_hex, sizeof(response_hex));
                        strcat(response_hex, "\r\n");

                        tinyusb_cdcacm_write_queue(itf, (uint8_t*)response_hex, strlen(response_hex));
                        tinyusb_cdcacm_write_flush(itf, 0);
                    } else {
                        // Timeout waiting for response
                        const char *timeout_msg = "TIMEOUT: No response from HLK-TX510\r\n";
                        tinyusb_cdcacm_write_queue(itf, (uint8_t*)timeout_msg, strlen(timeout_msg));
                        tinyusb_cdcacm_write_flush(itf, 0);
                    }
                } else {
                    const char *error_msg = "ERROR: Failed to queue command\r\n";
                    tinyusb_cdcacm_write_queue(itf, (uint8_t*)error_msg, strlen(error_msg));
                    tinyusb_cdcacm_write_flush(itf, 0);
                }
            } else {
                const char *error_msg = "ERROR: Invalid hex format\r\n";
                tinyusb_cdcacm_write_queue(itf, (uint8_t*)error_msg, strlen(error_msg));
                tinyusb_cdcacm_write_flush(itf, 0);
            }
        } else {
            // Echo back for non-command data
            ESP_LOGI(TAG, "Echoing back non-command data");
            tinyusb_cdcacm_write_queue(itf, buf, rx_size);
            tinyusb_cdcacm_write_flush(itf, 0);
        }
    } else {
        ESP_LOGE(TAG, "Read error");
    }
}

void tinyusb_cdc_line_state_changed_callback(int itf, cdcacm_event_t *event)
{
    int dtr = event->line_state_changed_data.dtr;
    int rts = event->line_state_changed_data.rts;
    ESP_LOGI(TAG, "Line state changed on channel %d: DTR:%d, RTS:%d", itf, dtr, rts);
}

void app_main(void)
{
    ESP_LOGI(TAG, "Starting ESP32-S3 to HLK-TX510 Communication Bridge");

    // Initialize UART for HLK-TX510 communication
    ESP_LOGI(TAG, "Initializing UART for HLK-TX510");
    ESP_ERROR_CHECK(hlk_uart_init());

    // Create queues for communication between USB CDC and UART
    hlk_cmd_queue = xQueueCreate(5, HLK_CMD_SIZE);
    hlk_response_queue = xQueueCreate(5, HLK_RESPONSE_MAX_SIZE);

    if (hlk_cmd_queue == NULL || hlk_response_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create communication queues");
        return;
    }

    // Create HLK communication task
    BaseType_t task_created = xTaskCreate(
        hlk_communication_task,
        "hlk_comm_task",
        4096,
        NULL,
        5,
        NULL
    );

    if (task_created != pdPASS) {
        ESP_LOGE(TAG, "Failed to create HLK communication task");
        return;
    }

    ESP_LOGI(TAG, "USB initialization");
    const tinyusb_config_t tusb_cfg = {
        .device_descriptor = NULL,
        .string_descriptor = NULL,
        .external_phy = false,
        .configuration_descriptor = NULL,
    };

    ESP_ERROR_CHECK(tinyusb_driver_install(&tusb_cfg));

    tinyusb_config_cdcacm_t acm_cfg = {
        .usb_dev = TINYUSB_USBDEV_0,
        .cdc_port = TINYUSB_CDC_ACM_0,
        .rx_unread_buf_sz = 64,
        .callback_rx = &tinyusb_cdc_rx_callback, // the first way to register a callback
        .callback_rx_wanted_char = NULL,
        .callback_line_state_changed = NULL,
        .callback_line_coding_changed = NULL
    };

    ESP_ERROR_CHECK(tusb_cdc_acm_init(&acm_cfg));
    /* the second way to register a callback */
    ESP_ERROR_CHECK(tinyusb_cdcacm_register_callback(
                        TINYUSB_CDC_ACM_0,
                        CDC_EVENT_LINE_STATE_CHANGED,
                        &tinyusb_cdc_line_state_changed_callback));

#if (CONFIG_TINYUSB_CDC_COUNT > 1)
    acm_cfg.cdc_port = TINYUSB_CDC_ACM_1;
    ESP_ERROR_CHECK(tusb_cdc_acm_init(&acm_cfg));
    ESP_ERROR_CHECK(tinyusb_cdcacm_register_callback(
                        TINYUSB_CDC_ACM_1,
                        CDC_EVENT_LINE_STATE_CHANGED,
                        &tinyusb_cdc_line_state_changed_callback));
#endif

    ESP_LOGI(TAG, "USB initialization DONE");
    ESP_LOGI(TAG, "ESP32-S3 to HLK-TX510 Communication Bridge ready!");
    ESP_LOGI(TAG, "Send 18-character hex strings (9 bytes) via USB CDC to communicate with HLK-TX510");
    ESP_LOGI(TAG, "Example: 010203040506070809");
}
