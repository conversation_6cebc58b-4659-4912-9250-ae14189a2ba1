/*
 * SPDX-FileCopyrightText: 2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */

#include <stdint.h>
#include <string.h>
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "usb/usb_host.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "sdkconfig.h"

static const char *TAG = "hlk_bridge";

// UART configuration for PC communication (COM6)
#define PC_UART_PORT_NUM       UART_NUM_0  // Default UART for ESP32-S3
#define PC_UART_BAUD_RATE      115200
#define PC_UART_BUF_SIZE       1024

// HLK-TX510 Protocol definitions
#define HLK_SYNC_WORD_1        0xEF
#define HLK_SYNC_WORD_2        0xAA
#define HLK_CMD_START_RECOG    0x12
#define HLK_CMD_REGISTER_FACE  0x13
#define HLK_CMD_DELETE_USER    0x20
#define HLK_CMD_DELETE_ALL     0x21
#define HLK_CMD_BACKLIGHT_OFF  0xC0
#define HLK_CMD_BACKLIGHT_ON   0xC0
#define HLK_CMD_DISPLAY_OFF    0xC1
#define HLK_CMD_DISPLAY_ON     0xC1
#define HLK_CMD_LIGHT_OFF      0xC2
#define HLK_CMD_LIGHT_ON       0xC2

#define HLK_RESPONSE_MAX_SIZE  256
#define HLK_CMD_MAX_SIZE       32

// USB Host configuration
#define USB_HOST_TASK_PRIORITY 5
#define USB_HOST_TASK_STACK_SIZE 4096

// Queues for communication
static QueueHandle_t pc_to_hlk_queue;
static QueueHandle_t hlk_to_pc_queue;

// USB Host handles
static usb_host_client_handle_t usb_client_handle;
static usb_device_handle_t hlk_device_handle = NULL;

// Buffers (will be allocated as needed in functions)

/**
 * Initialize UART for PC communication (COM6)
 */
static esp_err_t pc_uart_init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = PC_UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    // Install UART driver (UART0 uses default pins)
    esp_err_t ret = uart_driver_install(PC_UART_PORT_NUM, PC_UART_BUF_SIZE * 2, 0, 0, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install PC UART driver: %s", esp_err_to_name(ret));
        return ret;
    }

    // Configure UART parameters
    ret = uart_param_config(PC_UART_PORT_NUM, &uart_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure PC UART parameters: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "PC UART initialized for communication with PC (COM6)");
    return ESP_OK;
}

/**
 * Calculate checksum for HLK-TX510 command
 */
static uint8_t hlk_calculate_checksum(const uint8_t *data, size_t len)
{
    uint8_t checksum = 0;
    for (size_t i = 2; i < len - 1; i++) {  // Skip sync word and checksum byte
        checksum += data[i];
    }
    return checksum;
}

/**
 * Build HLK-TX510 command packet
 */
static size_t hlk_build_command(uint8_t msg_id, const uint8_t *data, size_t data_len, uint8_t *output)
{
    size_t packet_len = 8 + data_len;  // 2 sync + 1 msgid + 4 size + data + 1 checksum

    output[0] = HLK_SYNC_WORD_1;  // EF
    output[1] = HLK_SYNC_WORD_2;  // AA
    output[2] = msg_id;
    output[3] = 0x00;  // Size bytes (little endian)
    output[4] = 0x00;
    output[5] = 0x00;
    output[6] = (uint8_t)data_len;

    // Copy data
    if (data_len > 0 && data != NULL) {
        memcpy(&output[7], data, data_len);
    }

    // Calculate and add checksum
    output[packet_len - 1] = hlk_calculate_checksum(output, packet_len);

    return packet_len;
}

/**
 * Send command to HLK-TX510 via USB Host
 */
static esp_err_t hlk_send_usb_command(const uint8_t *cmd_packet, size_t packet_len)
{
    if (hlk_device_handle == NULL) {
        ESP_LOGE(TAG, "HLK-TX510 device not connected");
        return ESP_ERR_INVALID_STATE;
    }

    // TODO: Implement USB Host transfer to HLK-TX510
    // This will be implemented in the next step
    ESP_LOGI(TAG, "Sending %d bytes to HLK-TX510 via USB:", packet_len);
    ESP_LOG_BUFFER_HEX(TAG, cmd_packet, packet_len);

    return ESP_OK;
}

/**
 * USB Host event callback
 */
static void usb_host_event_callback(const usb_host_client_event_msg_t *event_msg, void *arg)
{
    switch (event_msg->event) {
        case USB_HOST_CLIENT_EVENT_NEW_DEV:
            ESP_LOGI(TAG, "New USB device connected");
            // TODO: Check if it's HLK-TX510 and open device
            break;
        case USB_HOST_CLIENT_EVENT_DEV_GONE:
            ESP_LOGI(TAG, "USB device disconnected");
            hlk_device_handle = NULL;
            break;
        default:
            break;
    }
}

/**
 * Initialize USB Host for HLK-TX510 communication
 */
static esp_err_t usb_host_init(void)
{
    // Install USB Host driver
    const usb_host_config_t host_config = {
        .skip_phy_setup = false,
        .intr_flags = ESP_INTR_FLAG_LEVEL1,
    };

    esp_err_t ret = usb_host_install(&host_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install USB Host driver: %s", esp_err_to_name(ret));
        return ret;
    }

    // Register USB Host client
    const usb_host_client_config_t client_config = {
        .is_synchronous = false,
        .max_num_event_msg = 5,
        .async = {
            .client_event_callback = usb_host_event_callback,
            .callback_arg = NULL,
        }
    };

    ret = usb_host_client_register(&client_config, &usb_client_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register USB Host client: %s", esp_err_to_name(ret));
        usb_host_uninstall();
        return ret;
    }

    ESP_LOGI(TAG, "USB Host initialized for HLK-TX510 communication");
    return ESP_OK;
}

/**
 * USB Host task to handle USB events
 */
static void usb_host_task(void *pvParameters)
{
    ESP_LOGI(TAG, "USB Host task started");

    while (1) {
        // Handle USB Host events
        uint32_t event_flags;
        usb_host_lib_handle_events(portMAX_DELAY, &event_flags);

        if (event_flags & USB_HOST_LIB_EVENT_FLAGS_NO_CLIENTS) {
            ESP_LOGW(TAG, "No USB clients");
        }
        if (event_flags & USB_HOST_LIB_EVENT_FLAGS_ALL_FREE) {
            ESP_LOGI(TAG, "All USB devices freed");
        }
    }
}

/**
 * Task to handle PC to HLK-TX510 communication
 */
static void pc_to_hlk_task(void *pvParameters)
{
    char pc_cmd_buffer[64];
    uint8_t hlk_packet[HLK_CMD_MAX_SIZE];

    ESP_LOGI(TAG, "PC to HLK-TX510 communication task started");

    while (1) {
        // Read commands from PC UART
        int len = uart_read_bytes(PC_UART_PORT_NUM, pc_cmd_buffer, sizeof(pc_cmd_buffer) - 1, pdMS_TO_TICKS(100));
        if (len > 0) {
            pc_cmd_buffer[len] = '\0';

            // Remove newlines and whitespace
            char *trimmed = pc_cmd_buffer;
            while (*trimmed == ' ' || *trimmed == '\t' || *trimmed == '\r' || *trimmed == '\n') {
                trimmed++;
            }

            size_t trimmed_len = strlen(trimmed);
            while (trimmed_len > 0 && (trimmed[trimmed_len-1] == ' ' || trimmed[trimmed_len-1] == '\t' ||
                                       trimmed[trimmed_len-1] == '\r' || trimmed[trimmed_len-1] == '\n')) {
                trimmed[--trimmed_len] = '\0';
            }

            ESP_LOGI(TAG, "Received from PC: %s", trimmed);

            // Parse command and build HLK-TX510 packet
            if (strcmp(trimmed, "START") == 0) {
                size_t packet_len = hlk_build_command(HLK_CMD_START_RECOG, NULL, 0, hlk_packet);
                hlk_send_usb_command(hlk_packet, packet_len);
                uart_write_bytes(PC_UART_PORT_NUM, "START command sent\r\n", 20);
            } else if (strcmp(trimmed, "REGISTER") == 0) {
                size_t packet_len = hlk_build_command(HLK_CMD_REGISTER_FACE, NULL, 0, hlk_packet);
                hlk_send_usb_command(hlk_packet, packet_len);
                uart_write_bytes(PC_UART_PORT_NUM, "REGISTER command sent\r\n", 23);
            } else if (strcmp(trimmed, "DELETE_ALL") == 0) {
                size_t packet_len = hlk_build_command(HLK_CMD_DELETE_ALL, NULL, 0, hlk_packet);
                hlk_send_usb_command(hlk_packet, packet_len);
                uart_write_bytes(PC_UART_PORT_NUM, "DELETE_ALL command sent\r\n", 25);
            } else {
                uart_write_bytes(PC_UART_PORT_NUM, "Unknown command. Use: START, REGISTER, DELETE_ALL\r\n", 51);
            }
        }
    }
}



void app_main(void)
{
    ESP_LOGI(TAG, "Starting ESP32-S3 to HLK-TX510 USB Communication Bridge");

    // Initialize PC UART communication
    ESP_LOGI(TAG, "Initializing PC UART communication");
    ESP_ERROR_CHECK(pc_uart_init());

    // Initialize USB Host for HLK-TX510
    ESP_LOGI(TAG, "Initializing USB Host for HLK-TX510");
    ESP_ERROR_CHECK(usb_host_init());

    // Create queues for communication
    pc_to_hlk_queue = xQueueCreate(5, 64);  // PC command queue
    hlk_to_pc_queue = xQueueCreate(5, HLK_RESPONSE_MAX_SIZE);  // HLK response queue

    if (pc_to_hlk_queue == NULL || hlk_to_pc_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create communication queues");
        return;
    }

    // Create USB Host task
    BaseType_t task_created = xTaskCreate(
        usb_host_task,
        "usb_host_task",
        USB_HOST_TASK_STACK_SIZE,
        NULL,
        USB_HOST_TASK_PRIORITY,
        NULL
    );

    if (task_created != pdPASS) {
        ESP_LOGE(TAG, "Failed to create USB Host task");
        return;
    }

    // Create PC to HLK communication task
    task_created = xTaskCreate(
        pc_to_hlk_task,
        "pc_to_hlk_task",
        4096,
        NULL,
        5,
        NULL
    );

    if (task_created != pdPASS) {
        ESP_LOGE(TAG, "Failed to create PC to HLK communication task");
        return;
    }

    ESP_LOGI(TAG, "ESP32-S3 to HLK-TX510 USB Communication Bridge ready!");
    ESP_LOGI(TAG, "Connect HLK-TX510 to USB port and send commands via UART (COM6):");
    ESP_LOGI(TAG, "Available commands: START, REGISTER, DELETE_ALL");

    // Send welcome message to PC
    const char *welcome_msg = "\r\n=== ESP32-S3 to HLK-TX510 Bridge Ready ===\r\n";
    uart_write_bytes(PC_UART_PORT_NUM, welcome_msg, strlen(welcome_msg));
    const char *help_msg = "Commands: START, REGISTER, DELETE_ALL\r\n> ";
    uart_write_bytes(PC_UART_PORT_NUM, help_msg, strlen(help_msg));
}
