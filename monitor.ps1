# ESP-IDF Monitor Script for ESP32-S3 HLK-TX510 Communication Bridge
# This script monitors the ESP32-S3 debug output

param(
    [string]$Port = "COM3"  # Default COM port, change as needed
)

Write-Host "Starting ESP32-S3 Monitor on port $Port..." -ForegroundColor Green
Write-Host "Press Ctrl+] to exit monitor" -ForegroundColor Yellow

# Set ESP-IDF path
$env:IDF_PATH = "D:\esp\v5.1.2\esp-idf"

# Set Python environment path
$PYTHON_ENV = "D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe"

# Start monitor
& $PYTHON_ENV "D:\esp\v5.1.2\esp-idf\tools\idf.py" -p $Port monitor
