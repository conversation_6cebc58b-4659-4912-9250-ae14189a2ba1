[0/1] Re-running CMake...

-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Manifest files have changed, solving dependencies.

.NOTICE: Updating lock file at D:\vscode\projects-lvgl\tusb_serial_device\dependencies.lock

NOTICE: Processing 1 dependencies:

NOTICE: [1/1] idf (5.1.2)

-- Project sdkconfig file D:/vscode/projects-lvgl/tusb_serial_device/sdkconfig
Loading defaults file D:/vscode/projects-lvgl/tusb_serial_device/sdkconfig.defaults...

-- App "tusb_serial_device" version: 1
-- Adding linker script D:/vscode/projects-lvgl/tusb_serial_device/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_system/ld/esp32s3/sections.ld.in
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_coex esp_common esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: D:/esp/v5.1.2/esp-idf/components/app_trace D:/esp/v5.1.2/esp-idf/components/app_update D:/esp/v5.1.2/esp-idf/components/bootloader D:/esp/v5.1.2/esp-idf/components/bootloader_support D:/esp/v5.1.2/esp-idf/components/bt D:/esp/v5.1.2/esp-idf/components/cmock D:/esp/v5.1.2/esp-idf/components/console D:/esp/v5.1.2/esp-idf/components/cxx D:/esp/v5.1.2/esp-idf/components/driver D:/esp/v5.1.2/esp-idf/components/efuse D:/esp/v5.1.2/esp-idf/components/esp-tls D:/esp/v5.1.2/esp-idf/components/esp_adc D:/esp/v5.1.2/esp-idf/components/esp_app_format D:/esp/v5.1.2/esp-idf/components/esp_coex D:/esp/v5.1.2/esp-idf/components/esp_common D:/esp/v5.1.2/esp-idf/components/esp_eth D:/esp/v5.1.2/esp-idf/components/esp_event D:/esp/v5.1.2/esp-idf/components/esp_gdbstub D:/esp/v5.1.2/esp-idf/components/esp_hid D:/esp/v5.1.2/esp-idf/components/esp_http_client D:/esp/v5.1.2/esp-idf/components/esp_http_server D:/esp/v5.1.2/esp-idf/components/esp_https_ota D:/esp/v5.1.2/esp-idf/components/esp_https_server D:/esp/v5.1.2/esp-idf/components/esp_hw_support D:/esp/v5.1.2/esp-idf/components/esp_lcd D:/esp/v5.1.2/esp-idf/components/esp_local_ctrl D:/esp/v5.1.2/esp-idf/components/esp_mm D:/esp/v5.1.2/esp-idf/components/esp_netif D:/esp/v5.1.2/esp-idf/components/esp_netif_stack D:/esp/v5.1.2/esp-idf/components/esp_partition D:/esp/v5.1.2/esp-idf/components/esp_phy D:/esp/v5.1.2/esp-idf/components/esp_pm D:/esp/v5.1.2/esp-idf/components/esp_psram D:/esp/v5.1.2/esp-idf/components/esp_ringbuf D:/esp/v5.1.2/esp-idf/components/esp_rom D:/esp/v5.1.2/esp-idf/components/esp_system D:/esp/v5.1.2/esp-idf/components/esp_timer D:/esp/v5.1.2/esp-idf/components/esp_wifi D:/esp/v5.1.2/esp-idf/components/espcoredump D:/esp/v5.1.2/esp-idf/components/esptool_py D:/esp/v5.1.2/esp-idf/components/fatfs D:/esp/v5.1.2/esp-idf/components/freertos D:/esp/v5.1.2/esp-idf/components/hal D:/esp/v5.1.2/esp-idf/components/heap D:/esp/v5.1.2/esp-idf/components/http_parser D:/esp/v5.1.2/esp-idf/components/idf_test D:/esp/v5.1.2/esp-idf/components/ieee802154 D:/esp/v5.1.2/esp-idf/components/json D:/esp/v5.1.2/esp-idf/components/log D:/esp/v5.1.2/esp-idf/components/lwip D:/vscode/projects-lvgl/tusb_serial_device/main D:/esp/v5.1.2/esp-idf/components/mbedtls D:/esp/v5.1.2/esp-idf/components/mqtt D:/esp/v5.1.2/esp-idf/components/newlib D:/esp/v5.1.2/esp-idf/components/nvs_flash D:/esp/v5.1.2/esp-idf/components/openthread D:/esp/v5.1.2/esp-idf/components/partition_table D:/esp/v5.1.2/esp-idf/components/perfmon D:/esp/v5.1.2/esp-idf/components/protobuf-c D:/esp/v5.1.2/esp-idf/components/protocomm D:/esp/v5.1.2/esp-idf/components/pthread D:/esp/v5.1.2/esp-idf/components/sdmmc D:/esp/v5.1.2/esp-idf/components/soc D:/esp/v5.1.2/esp-idf/components/spi_flash D:/esp/v5.1.2/esp-idf/components/spiffs D:/esp/v5.1.2/esp-idf/components/tcp_transport D:/esp/v5.1.2/esp-idf/components/touch_element D:/esp/v5.1.2/esp-idf/components/ulp D:/esp/v5.1.2/esp-idf/components/unity D:/esp/v5.1.2/esp-idf/components/usb D:/esp/v5.1.2/esp-idf/components/vfs D:/esp/v5.1.2/esp-idf/components/wear_levelling D:/esp/v5.1.2/esp-idf/components/wifi_provisioning D:/esp/v5.1.2/esp-idf/components/wpa_supplicant D:/esp/v5.1.2/esp-idf/components/xtensa
-- Configuring done
-- Generating done
-- Build files have been written to: D:/vscode/projects-lvgl/tusb_serial_device/build
[1/914] Generating memory.ld linker script...

[2/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/esp_mem.c.obj

[3/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/sha/dma/esp_sha_gdma_impl.c.obj

[4/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/aes/dma/esp_aes_gdma_impl.c.obj

[5/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/threading.c.obj

[6/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/timing.c.obj

[7/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version.c.obj

[8/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version_features.c.obj

[9/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/aes/esp_aes_xts.c.obj

[10/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/esp_hardware.c.obj

[11/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/esp32s3/bignum.c.obj

[12/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/aes/esp_aes_common.c.obj

[13/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/esp_timing.c.obj

[14/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/sha/dma/esp_sha1.c.obj

[15/914] Building C object esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition.c.obj

[16/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/sha/esp_sha.c.obj

[17/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/md/esp_md.c.obj

[18/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/sha/dma/esp_sha512.c.obj

[19/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/sha/dma/esp_sha256.c.obj

[20/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/crypto_shared_gdma/esp_crypto_shared_gdma.c.obj

[21/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/sha/dma/sha.c.obj

[22/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/esp_bignum.c.obj

[23/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/aes/esp_aes_gcm.c.obj

[24/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/aes/dma/esp_aes.c.obj

[25/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_reader.c.obj

[26/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_trace.c.obj

[27/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/debug.c.obj

[28/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_debug_helpers_generated.c.obj

[29/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write.c.obj

[30/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/esp_ds/esp_rsa_sign_alt.c.obj

[31/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_csr.c.obj

[32/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_csr.c.obj

[33/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/pkcs7.c.obj

[34/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_create.c.obj

[35/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crl.c.obj

[36/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_crt.c.obj

[37/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cache.c.obj

[38/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ciphersuites.c.obj

[39/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cookie.c.obj

[40/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ticket.c.obj

[41/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_client.c.obj

[42/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_keys.c.obj

[43/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509.c.obj

[44/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_server.c.obj

[45/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_generic.c.obj

[46/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_client.c.obj

[47/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_client.c.obj

[48/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/esp_platform_time.c.obj

[49/914] Building C object esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj

[50/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_msg.c.obj

[51/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/mbedtls_debug.c.obj

[52/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj

[53/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj

[54/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj

[55/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_server.c.obj

[56/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj

[57/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj

[58/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj

[59/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj

[60/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj

[61/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj

[62/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj

[63/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/idf/bootloader_sha.c.obj

[64/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj

[65/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj

[66/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/D_/esp/v5.1.2/esp-idf/components/mbedtls/port/net_sockets.c.obj

[67/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj

[68/914] Building C object esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_app_desc.c.obj

[69/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls.c.obj

[70/914] Building C object esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition_target.c.obj

[71/914] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/port/esp32s3/ext_mem_layout.c.obj

[72/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crt.c.obj

[73/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj

[74/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_brownout_hook.c.obj

[75/914] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_cache.c.obj

[76/914] Building C object esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_ops.c.obj

[77/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_issi.c.obj

[78/914] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_mmu_map.c.obj

[79/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_gd.c.obj

[80/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic.c.obj

[81/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_drivers.c.obj

[82/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_th.c.obj

[83/914] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj

[84/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_winbond.c.obj

[85/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp32s3/spi_flash_oct_flash_init.c.obj

[86/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_hpm_enable.c.obj

[87/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_boya.c.obj

[88/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic_opi.c.obj

[89/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_generic.c.obj

[90/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/memspi_host_driver.c.obj

[91/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_noos.c.obj

[92/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/cache_utils.c.obj

[93/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_mmap.c.obj

[94/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_ops.c.obj

[95/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj

[96/914] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_semaphore.c.obj

[97/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj

[98/914] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_cond_var.c.obj

[99/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/crosscore_int.c.obj

[100/914] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_local_storage.c.obj

[101/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_app.c.obj

[102/914] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_rwlock.c.obj

[103/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_spi_init.c.obj

[104/914] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread.c.obj

[105/914] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_api.c.obj

[106/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_ipc.c.obj

[107/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/freertos_hooks.c.obj

[108/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_system.c.obj

[109/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/int_wdt.c.obj

[110/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/debug_stubs.c.obj

[111/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/system_time.c.obj

[112/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/stack_check.c.obj

[113/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/ubsan.c.obj

[114/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/xt_wdt.c.obj

[115/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt_impl_timergroup.c.obj

[116/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/panic.c.obj

[117/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/reset_reason.c.obj

[118/914] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/highint_hdl.S.obj

[119/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/cpu_start.c.obj

[120/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt.c.obj

[121/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/panic_handler.c.obj

[122/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/esp_system_chip.c.obj

[123/914] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/panic_handler_asm.S.obj

[124/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/debug_stubs.c.obj

[125/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/cache_err_int.c.obj

[126/914] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/esp_ipc_isr_handler.S.obj

[127/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/startup.c.obj

[128/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/clk.c.obj

[129/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/panic_arch.c.obj

[130/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/apb_backup_dma.c.obj

[131/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/brownout.c.obj

[132/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/system_internal.c.obj

[133/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/debug_helpers.c.obj

[134/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_se.c.obj

[135/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ripemd160.c.obj

[136/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/trax.c.obj

[137/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/esp_ipc_isr.c.obj

[138/914] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/expression_with_stack.c.obj

[139/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha1.c.obj

[140/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha512.c.obj

[141/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_util.c.obj

[142/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_its_file.c.obj

[143/914] Building C object esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/Hacl_Curve25519_joined.c.obj

[144/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_storage.c.obj

[145/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa_alt_helpers.c.obj

[146/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha3.c.obj

[147/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_slot_management.c.obj

[148/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha256.c.obj

[149/914] Building C object esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/x25519.c.obj

[150/914] Building C object esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/everest.c.obj

[151/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesni.c.obj

[152/914] Building C object esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m/p256-m.c.obj

[153/914] Building C object esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m_driver_entrypoints.c.obj

[154/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesce.c.obj

[155/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod.c.obj

[156/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1parse.c.obj

[157/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chacha20.c.obj

[158/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aes.c.obj

[159/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod_raw.c.obj

[160/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ccm.c.obj

[161/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa.c.obj

[162/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1write.c.obj

[163/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/base64.c.obj

[164/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chachapoly.c.obj

[165/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/camellia.c.obj

[166/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_core.c.obj

[167/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aria.c.obj

[168/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/des.c.obj

[169/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves_new.c.obj

[170/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/dhm.c.obj

[171/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cmac.c.obj

[172/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum.c.obj

[173/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/constant_time.c.obj

[174/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecjpake.c.obj

[175/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ctr_drbg.c.obj

[176/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy_poll.c.obj

[177/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdh.c.obj

[178/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdsa.c.obj

[179/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher_wrap.c.obj

[180/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher.c.obj

[181/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/gcm.c.obj

[182/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hkdf.c.obj

[183/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/nist_kw.c.obj

[184/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/memory_buffer_alloc.c.obj

[185/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md5.c.obj

[186/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy.c.obj

[187/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/error.c.obj

[188/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/padlock.c.obj

[189/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hmac_drbg.c.obj

[190/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves.c.obj

[191/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lms.c.obj

[192/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lmots.c.obj

[193/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp.c.obj

[194/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk.c.obj

[195/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md.c.obj

[196/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs12.c.obj

[197/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk_wrap.c.obj

[198/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform.c.obj

[199/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pem.c.obj

[200/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs5.c.obj

[201/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform_util.c.obj

[202/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/oid.c.obj

[203/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/poly1305.c.obj

[204/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_client.c.obj

[205/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkwrite.c.obj

[206/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_driver_wrappers_no_static.c.obj

[207/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_aead.c.obj

[208/914] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj

[209/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkparse.c.obj

[210/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ffdh.c.obj

[211/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_cipher.c.obj

[212/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ecp.c.obj

[213/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_rsa.c.obj

[214/914] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj

[215/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_pake.c.obj

[216/914] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj

[217/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_hash.c.obj

[218/914] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj

[219/914] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj

[220/914] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj

[221/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_mac.c.obj

[222/914] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj

[223/914] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj

[224/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj

[225/914] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj

[226/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj

[227/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj

[228/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/gpio_hal.c.obj

[229/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj

[230/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/rtc_io_hal.c.obj

[231/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal_iram.c.obj

[232/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal.c.obj

[233/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/systimer_hal.c.obj

[234/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_encrypt_hal_iram.c.obj

[235/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/clk_tree_hal.c.obj

[236/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj

[237/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal.c.obj

[238/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_oneshot_hal.c.obj

[239/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/rmt_hal.c.obj

[240/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/timer_hal.c.obj

[241/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_hal_common.c.obj

[242/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal_iram.c.obj

[243/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/pcnt_hal.c.obj

[244/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal.c.obj

[245/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/gdma_hal.c.obj

[246/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/sdm_hal.c.obj

[247/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal_iram.c.obj

[248/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal_iram.c.obj

[249/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/twai_hal_iram.c.obj

[250/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal.c.obj

[251/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mcpwm_hal.c.obj

[252/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/twai_hal.c.obj

[253/914] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto.c.obj

[254/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/brownout_hal.c.obj

[255/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/sha_hal.c.obj

[256/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/aes_hal.c.obj

[257/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/hmac_hal.c.obj

[258/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/lcd_hal.c.obj

[259/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/ds_hal.c.obj

[260/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal.c.obj

[261/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal_iram.c.obj

[262/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_hal.c.obj

[263/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/i2s_hal.c.obj

[264/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal.c.obj

[265/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal_iram.c.obj

[266/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_phy_hal.c.obj

[267/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/touch_sensor_hal.c.obj

[268/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hd_hal.c.obj

[269/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/xt_wdt_hal.c.obj

[270/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/touch_sensor_hal.c.obj

[271/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_hal.c.obj

[272/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal_gpspi.c.obj

[273/914] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj

[274/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/rtc_cntl_hal.c.obj

[275/914] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_dwc_hal.c.obj

[276/914] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj

[277/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj

[278/914] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/port/esp32s3/memory_layout.c.obj

[279/914] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/log_freertos.c.obj

[280/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj

[281/914] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/port/memory_layout_utils.c.obj

[282/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj

[283/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj

[284/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj

[285/914] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps_init.c.obj

[286/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj

[287/914] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/multi_heap.c.obj

[288/914] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps.c.obj

[289/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj

[290/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj

[291/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj

[292/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj

[293/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj

[294/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj

[295/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj

[296/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj

[297/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj

[298/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj

[299/914] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj

[300/914] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/tlsf/tlsf.c.obj

[301/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj

[302/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj

[303/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj

[304/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/hw_random.c.obj

[305/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/revision.c.obj

[306/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clk_ctrl_os.c.obj

[307/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mac_addr.c.obj

[308/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/intr_alloc.c.obj

[309/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/regi2c_ctrl.c.obj

[310/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/periph_ctrl.c.obj

[311/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modem.c.obj

[312/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rtc_module.c.obj

[313/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_clk.c.obj

[314/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_gpio.c.obj

[315/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_clk_tree.c.obj

[316/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp_clk_tree_common.c.obj

[317/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/io_mux.c.obj

[318/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sar_periph_ctrl_common.c.obj

[319/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/async_memcpy_impl_gdma.c.obj

[320/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_hmac.c.obj

[321/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/esp_async_memcpy.c.obj

[322/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_cpu.c.obj

[323/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_ds.c.obj

[324/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mspi_timing_tuning.c.obj

[325/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/adc_share_hw_ctrl.c.obj

[326/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_wake_stub.c.obj

[327/914] Building CXX object esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_exception_stubs.cpp.obj

[328/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/mspi_timing_config.c.obj

[329/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj

[330/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj

[331/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modes.c.obj

[332/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj

[333/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma.c.obj

[334/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj

[335/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj

[336/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp_memprot_conv.c.obj

[337/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/list.c.obj

[338/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/heap_idf.c.obj

[339/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj

[340/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/sar_periph_ctrl.c.obj

[341/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/port.c.obj

[342/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/croutine.c.obj

[343/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/idf_additions.c.obj

[344/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/queue.c.obj

[345/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/timers.c.obj

[346/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/stream_buffer.c.obj

[347/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/port_common.c.obj

[348/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-openocd.c.obj

[349/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/port_systick.c.obj

[350/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/event_groups.c.obj

[351/914] Building ASM object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/portasm.S.obj

[352/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/app_startup.c.obj

[353/914] Building ASM object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/xtensa_context.S.obj

[354/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c.obj

[355/914] Building ASM object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/xtensa_vectors.S.obj

[356/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c.obj

[357/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/freertos_v8_compat.c.obj

[358/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/abort.c.obj

[359/914] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_memprot.c.obj

[360/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/assert.c.obj

[361/914] Building CXX object esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_guards.cpp.obj

[362/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/newlib_init.c.obj

[363/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/reent_init.c.obj

[364/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/pthread.c.obj

[365/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/poll.c.obj

[366/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/heap.c.obj

[367/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/random.c.obj

[368/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/realpath.c.obj

[369/914] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/tasks.c.obj

[370/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/syscalls.c.obj

[371/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/locks.c.obj

[372/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/sysconf.c.obj

[373/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/termios.c.obj

[374/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/port/esp_time_impl.c.obj

[375/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/time.c.obj

[376/914] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/stdatomic.c.obj

[377/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/stats.c.obj

[378/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/sys.c.obj

[379/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/autoip.c.obj

[380/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/icmp.c.obj

[381/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4.c.obj

[382/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_addr.c.obj

[383/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_napt.c.obj

[384/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/igmp.c.obj

[385/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/timeouts.c.obj

[386/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/udp.c.obj

[387/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/dhcp6.c.obj

[388/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp_out.c.obj

[389/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/etharp.c.obj

[390/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp_in.c.obj

[391/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_frag.c.obj

[392/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp.c.obj

[393/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/dhcp.c.obj

[394/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/icmp6.c.obj

[395/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ethip6.c.obj

[396/914] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_common.c.obj

[397/914] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/system_time.c.obj

[398/914] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/ets_timer_legacy.c.obj

[399/914] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_systimer.c.obj

[400/914] Building C object esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/default_event_loop.c.obj

[401/914] Building C object esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/esp_event_private.c.obj

[402/914] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer.c.obj

[403/914] Building C object esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/esp_event.c.obj

[404/914] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/lib_printf.c.obj

[405/914] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/btbb_init.c.obj

[406/914] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_override.c.obj

[407/914] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_common.c.obj

[408/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition.cpp.obj

[409/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition_lookup.cpp.obj

[410/914] Building C object esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj

[411/914] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_eventfd.c.obj

[412/914] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_init.c.obj

[413/914] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_uart.c.obj

[414/914] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_console.c.obj

[415/914] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs.c.obj

[416/914] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_usb_serial_jtag.c.obj

[417/914] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_semihost.c.obj

[418/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/api_lib.c.obj

[419/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/err.c.obj

[420/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/if_api.c.obj

[421/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_types.cpp.obj

[422/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/sntp/sntp.c.obj

[423/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netbuf.c.obj

[424/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_item_hash_list.cpp.obj

[425/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/api_msg.c.obj

[426/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_handle_locked.cpp.obj

[427/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_pagemanager.cpp.obj

[428/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_handle_simple.cpp.obj

[429/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition_manager.cpp.obj

[430/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_page.cpp.obj

[431/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_cxx_api.cpp.obj

[432/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/tcpip.c.obj

[433/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_storage.cpp.obj

[434/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ip.c.obj

[435/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netifapi.c.obj

[436/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netdb.c.obj

[437/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/init.c.obj

[438/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/apps/sntp/sntp.c.obj

[439/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/def.c.obj

[440/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/mem.c.obj

[441/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/inet_chksum.c.obj

[442/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/apps/netbiosns/netbiosns.c.obj

[443/914] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_api.cpp.obj

[444/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/dns.c.obj

[445/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/memp.c.obj

[446/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/raw.c.obj

[447/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_peap_common.c.obj

[448/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/pbuf.c.obj

[449/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_common.c.obj

[450/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/sockets.c.obj

[451/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6_addr.c.obj

[452/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/netif.c.obj

[453/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_tls.c.obj

[454/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/inet6.c.obj

[455/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_tls_common.c.obj

[456/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_peap.c.obj

[457/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6_frag.c.obj

[458/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_mschapv2.c.obj

[459/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ethernet.c.obj

[460/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/bridgeif_fdb.c.obj

[461/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/slipif.c.obj

[462/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6.c.obj

[463/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/auth.c.obj

[464/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ccp.c.obj

[465/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap-md5.c.obj

[466/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ecp.c.obj

[467/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/bridgeif.c.obj

[468/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/demand.c.obj

[469/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/mld6.c.obj

[470/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/eui64.c.obj

[471/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/eap.c.obj

[472/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap_ms.c.obj

[473/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap-new.c.obj

[474/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/nd6.c.obj

[475/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/magic.c.obj

[476/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/mppe.c.obj

[477/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/multilink.c.obj

[478/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/fsm.c.obj

[479/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppol2tp.c.obj

[480/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppcrypt.c.obj

[481/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppos.c.obj

[482/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ppp.c.obj

[483/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ipcp.c.obj

[484/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ipv6cp.c.obj

[485/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/lcp.c.obj

[486/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppapi.c.obj

[487/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppoe.c.obj

[488/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/hooks/tcp_isn_default.c.obj

[489/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/vj.c.obj

[490/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/upap.c.obj

[491/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/utils.c.obj

[492/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/debug/lwip_debug.c.obj

[493/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/hooks/lwip_default_hooks.c.obj

[494/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/ping_sock.c.obj

[495/914] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_defaults.c.obj

[496/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/esp32xx/vfs_lwip.c.obj

[497/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/sockets_ext.c.obj

[498/914] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_objects.c.obj

[499/914] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_handlers.c.obj

[500/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/esp_ping.c.obj

[501/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/ping.c.obj

[502/914] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_lwip_defaults.c.obj

[503/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/freertos/sys_arch.c.obj

[504/914] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/esp_pbuf_ref.c.obj

[505/914] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_sntp.c.obj

[506/914] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/wlanif.c.obj

[507/914] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/ethernetif.c.obj

[508/914] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/dhcpserver/dhcpserver.c.obj

[509/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/port/eloop.c.obj

[510/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ap_config.c.obj

[511/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/port/os_xtensa.c.obj

[512/914] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_lwip.c.obj

[513/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/sta_info.c.obj

[514/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/comeback_token.c.obj

[515/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-siv.c.obj

[516/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-kdf.c.obj

[517/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/pmksa_cache_auth.c.obj

[518/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/wpa_auth_ie.c.obj

[519/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ieee802_11.c.obj

[520/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ieee802_1x.c.obj

[521/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/bitfield.c.obj

[522/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/dragonfly.c.obj

[523/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/ccmp.c.obj

[524/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/wpa_common.c.obj

[525/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/wpa_auth.c.obj

[526/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/dh_group5.c.obj

[527/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/sae.c.obj

[528/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha384-tlsprf.c.obj

[529/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-gcm.c.obj

[530/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/crypto_ops.c.obj

[531/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-prf.c.obj

[532/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-tlsprf.c.obj

[533/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-tlsprf.c.obj

[534/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/dh_groups.c.obj

[535/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/ms_funcs.c.obj

[536/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-tprf.c.obj

[537/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-prf.c.obj

[538/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_common/eap_wsc_common.c.obj

[539/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/ieee802_11_common.c.obj

[540/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha384-prf.c.obj

[541/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/chap.c.obj

[542/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/md4-internal.c.obj

[543/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/mschapv2.c.obj

[544/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap.c.obj

[545/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast_common.c.obj

[546/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/wpabuf.c.obj

[547/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/base64.c.obj

[548/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast_pac.c.obj

[549/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_ttls.c.obj

[550/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/pmksa_cache.c.obj

[551/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/wpa_ie.c.obj

[552/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/common.c.obj

[553/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/uuid.c.obj

[554/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/wpa_debug.c.obj

[555/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/ext_password.c.obj

[556/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast.c.obj

[557/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps.c.obj

[558/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/wpa.c.obj

[559/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_process.c.obj

[560/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/json.c.obj

[561/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_dev_attr.c.obj

[562/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_parse.c.obj

[563/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_build.c.obj

[564/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/scan.c.obj

[565/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa2_api_port.c.obj

[566/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_scan.c.obj

[567/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_common.c.obj

[568/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa_main.c.obj

[569/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/bss.c.obj

[570/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_common.c.obj

[571/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpas_glue.c.obj

[572/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/sae_pk.c.obj

[573/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_eap_client.c.obj

[574/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_enrollee.c.obj

[575/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_owe.c.obj

[576/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa3.c.obj

[577/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wps.c.obj

[578/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/fastpbkdf2.c.obj

[579/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_hostap.c.obj

[580/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls.c.obj

[581/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-wrap.c.obj

[582/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/des-internal.c.obj

[583/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/tls_mbedtls.c.obj

[584/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/rc4.c.obj

[585/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-unwrap.c.obj

[586/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-ccm.c.obj

[587/914] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_default_ap.c.obj

[588/914] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_default.c.obj

[589/914] Building C object esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/esp32s3/esp_coex_adapter.c.obj

[590/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c.obj

[591/914] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/smartconfig.c.obj

[592/914] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/mesh_event.c.obj

[593/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c.obj

[594/914] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_netif.c.obj

[595/914] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-ec.c.obj

[596/914] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_init.c.obj

[597/914] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp-tls-crypto/esp_tls_crypto.c.obj

[598/914] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/esp32s3/esp_adapter.c.obj

[599/914] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_cali.c.obj

[600/914] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/smartconfig_ack.c.obj

[601/914] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/deprecated/esp_adc_cal_common_legacy.c.obj

[602/914] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_cali_curve_fitting.c.obj

[603/914] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_error_capture.c.obj

[604/914] Building C object esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/src/esp_https_ota.c.obj

[605/914] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/esp32s3/curve_fitting_coefficients.c.obj

[606/914] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_common.c.obj

[607/914] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_oneshot.c.obj

[608/914] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/deprecated/esp32s3/esp_adc_cal_legacy.c.obj

[609/914] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_filter.c.obj

[610/914] Building ASM object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/port/xtensa/gdbstub-entry.S.obj

[611/914] Building C object esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/http_parser.c.obj

[612/914] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_continuous.c.obj

[613/914] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls.c.obj

[614/914] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/packet.c.obj

[615/914] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/gdbstub_transport.c.obj

[616/914] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_mbedtls.c.obj

[617/914] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/port/xtensa/gdbstub_xtensa.c.obj

[618/914] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/esp_eth_netif_glue.c.obj

[619/914] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/esp_eth.c.obj

[620/914] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/esp_eth_phy_802_3.c.obj

[621/914] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/gdbstub.c.obj

[622/914] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_utils.c.obj

[623/914] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_header.c.obj

[624/914] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport.c.obj

[625/914] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_auth.c.obj

[626/914] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_internal.c.obj

[627/914] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_socks_proxy.c.obj

[628/914] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_ssl.c.obj

[629/914] Building C object esp-idf/ulp/CMakeFiles/__idf_ulp.dir/ulp_common/ulp_adc.c.obj

[630/914] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/util/ctrl_sock.c.obj

[631/914] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_sess.c.obj

[632/914] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_ws.c.obj

[633/914] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_main.c.obj

[634/914] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_uri.c.obj

[635/914] Building C object esp-idf/ulp/CMakeFiles/__idf_ulp.dir/ulp_common/ulp_common.c.obj

[636/914] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_parse.c.obj

[637/914] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_ws.c.obj

[638/914] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_txrx.c.obj

[639/914] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/esp_http_client.c.obj

[640/914] Linking C static library esp-idf\ulp\libulp.a

[641/914] Linking C static library esp-idf\esp_https_ota\libesp_https_ota.a

[642/914] Linking C static library esp-idf\esp_http_server\libesp_http_server.a

[643/914] Linking C static library esp-idf\esp_http_client\libesp_http_client.a

[644/914] Linking C static library esp-idf\tcp_transport\libtcp_transport.a

[645/914] Linking C static library esp-idf\esp_gdbstub\libesp_gdbstub.a

[646/914] Linking C static library esp-idf\esp_eth\libesp_eth.a

[647/914] Linking C static library esp-idf\esp_adc\libesp_adc.a

[648/914] Linking C static library esp-idf\esp-tls\libesp-tls.a

[649/914] Linking C static library esp-idf\http_parser\libhttp_parser.a

[650/914] Linking C static library esp-idf\esp_wifi\libesp_wifi.a

[651/914] Linking C static library esp-idf\esp_coex\libesp_coex.a

[652/914] Performing build step for 'bootloader'

[0/1] Re-running CMake...

-- Building ESP-IDF components for target esp32s3
-- Project sdkconfig file D:/vscode/projects-lvgl/tusb_serial_device/sdkconfig
Compiler supported targets: xtensa-esp32s3-elf

-- Adding linker script D:/esp/v5.1.2/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- App "bootloader" version: v5.1.2
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/main/ld/esp32s3/bootloader.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/main/ld/esp32s3/bootloader.rom.ld
-- Components: bootloader bootloader_support efuse esp_app_format esp_common esp_hw_support esp_rom esp_system esptool_py freertos hal log main micro-ecc newlib partition_table soc spi_flash xtensa
-- Component paths: D:/esp/v5.1.2/esp-idf/components/bootloader D:/esp/v5.1.2/esp-idf/components/bootloader_support D:/esp/v5.1.2/esp-idf/components/efuse D:/esp/v5.1.2/esp-idf/components/esp_app_format D:/esp/v5.1.2/esp-idf/components/esp_common D:/esp/v5.1.2/esp-idf/components/esp_hw_support D:/esp/v5.1.2/esp-idf/components/esp_rom D:/esp/v5.1.2/esp-idf/components/esp_system D:/esp/v5.1.2/esp-idf/components/esptool_py D:/esp/v5.1.2/esp-idf/components/freertos D:/esp/v5.1.2/esp-idf/components/hal D:/esp/v5.1.2/esp-idf/components/log D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/main D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc D:/esp/v5.1.2/esp-idf/components/newlib D:/esp/v5.1.2/esp-idf/components/partition_table D:/esp/v5.1.2/esp-idf/components/soc D:/esp/v5.1.2/esp-idf/components/spi_flash D:/esp/v5.1.2/esp-idf/components/xtensa
-- Configuring done
-- Generating done
-- Build files have been written to: D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader
[1/1] cmd.exe /C "cd /D D:\vscode\projects-lvgl\tusb_serial_device\build\bootloader\esp-idf\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe D:/esp/v5.1.2/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader/bootloader.bin"

Bootloader binary size 0x51c0 bytes. 0x2e40 bytes (36%) free.

[653/912] Linking C static library esp-idf\wpa_supplicant\libwpa_supplicant.a

[654/912] Linking C static library esp-idf\esp_netif\libesp_netif.a

[655/912] Linking C static library esp-idf\lwip\liblwip.a

[656/912] Linking C static library esp-idf\vfs\libvfs.a

[657/912] Linking C static library esp-idf\esp_phy\libesp_phy.a

[658/912] Linking C static library esp-idf\nvs_flash\libnvs_flash.a

[659/912] Linking C static library esp-idf\esp_event\libesp_event.a

[660/912] Linking C static library esp-idf\esp_timer\libesp_timer.a

[661/912] Linking C static library esp-idf\esp_common\libesp_common.a

[662/912] Linking C static library esp-idf\cxx\libcxx.a

[663/912] Linking C static library esp-idf\newlib\libnewlib.a

[664/912] Linking C static library esp-idf\freertos\libfreertos.a

[665/912] Linking C static library esp-idf\esp_hw_support\libesp_hw_support.a

[666/912] Linking C static library esp-idf\soc\libsoc.a

[667/912] Linking C static library esp-idf\heap\libheap.a

[668/912] Linking C static library esp-idf\log\liblog.a

[669/912] Linking C static library esp-idf\hal\libhal.a

[670/912] Linking C static library esp-idf\esp_rom\libesp_rom.a

[671/912] Linking C static library esp-idf\esp_system\libesp_system.a

[672/912] Linking C static library esp-idf\pthread\libpthread.a

[673/912] Linking C static library esp-idf\spi_flash\libspi_flash.a

[674/912] Linking C static library esp-idf\esp_mm\libesp_mm.a

[675/912] Linking C static library esp-idf\app_update\libapp_update.a

[676/912] Linking C static library esp-idf\esp_partition\libesp_partition.a

[677/912] Linking C static library esp-idf\bootloader_support\libbootloader_support.a

[678/912] Linking C static library esp-idf\esp_app_format\libesp_app_format.a

[679/912] Linking CXX static library esp-idf\mbedtls\mbedtls\library\libmbedtls.a

[680/912] Linking CXX static library esp-idf\mbedtls\mbedtls\library\libmbedx509.a

[681/912] Linking CXX static library esp-idf\mbedtls\mbedtls\library\libmbedcrypto.a

[682/912] Linking CXX static library esp-idf\mbedtls\mbedtls\3rdparty\p256-m\libp256m.a

[683/912] Linking CXX static library esp-idf\mbedtls\mbedtls\3rdparty\everest\libeverest.a

[684/912] Building ASM object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_intr_asm.S.obj

[685/912] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj

[686/912] Building C object esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_trace.c.obj

[687/912] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj

[688/912] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_intr.c.obj

[689/912] Building C object esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_locks.c.obj

[690/912] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj

[691/912] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj

[692/912] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj

[693/912] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj

[694/912] Building C object esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_impl.c.obj

[695/912] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj

[696/912] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj

[697/912] Building C object esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/ringbuf.c.obj

[698/912] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj

[699/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/gpio/gpio_glitch_filter_ops.c.obj

[700/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/adc_dma_legacy.c.obj

[701/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/adc_legacy.c.obj

[702/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/gpio/gpio_pin_glitch_filter.c.obj

[703/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/gptimer/gptimer_priv.c.obj

[704/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/spi/spi_bus_lock.c.obj

[705/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/gpio/rtc_io.c.obj

[706/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/gpio/gpio.c.obj

[707/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/gptimer/gptimer.c.obj

[708/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/gpio/dedic_gpio.c.obj

[709/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/timer_legacy.c.obj

[710/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/i2s/i2s_tdm.c.obj

[711/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/i2s/i2s_std.c.obj

[712/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/mcpwm/mcpwm_cap.c.obj

[713/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/i2c/i2c.c.obj

[714/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/i2s/i2s_common.c.obj

[715/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/mcpwm/mcpwm_cmpr.c.obj

[716/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/mcpwm/mcpwm_com.c.obj

[717/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/mcpwm/mcpwm_sync.c.obj

[718/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/i2s/i2s_pdm.c.obj

[719/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/mcpwm/mcpwm_fault.c.obj

[720/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/mcpwm/mcpwm_gen.c.obj

[721/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/i2s_legacy.c.obj

[722/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/ledc/ledc.c.obj

[723/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/mcpwm/mcpwm_oper.c.obj

[724/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/rmt/rmt_common.c.obj

[725/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/rmt/rmt_encoder.c.obj

[726/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/spi/sdspi/sdspi_crc.c.obj

[727/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/sigma_delta_legacy.c.obj

[728/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/pcnt_legacy.c.obj

[729/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/rmt/rmt_rx.c.obj

[730/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/mcpwm/mcpwm_timer.c.obj

[731/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/sdmmc/sdmmc_transaction.c.obj

[732/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/sigma_delta/sdm.c.obj

[733/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/pcnt/pulse_cnt.c.obj

[734/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/mcpwm_legacy.c.obj

[735/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/spi/sdspi/sdspi_transaction.c.obj

[736/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/sdmmc/sdmmc_host.c.obj

[737/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/spi/gpspi/spi_common.c.obj

[738/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/rmt/rmt_tx.c.obj

[739/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/rmt_legacy.c.obj

[740/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/spi/sdspi/sdspi_host.c.obj

[741/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/spi/gpspi/spi_slave.c.obj

[742/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/rtc_temperature_legacy.c.obj

[743/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/touch_sensor/touch_sensor_common.c.obj

[744/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/spi/gpspi/spi_master.c.obj

[745/912] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/host_file_io.c.obj

[746/912] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/app_trace_util.c.obj

[747/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/usb_serial_jtag/usb_serial_jtag_connection_monitor.c.obj

[748/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/temperature_sensor/temperature_sensor.c.obj

[749/912] Building C object esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/esp_crt_bundle/esp_crt_bundle.c.obj

[750/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/touch_sensor/esp32s3/touch_sensor.c.obj

[751/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/spi/gpspi/spi_slave_hd.c.obj

[752/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/usb_serial_jtag/usb_serial_jtag.c.obj

[753/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/twai/twai.c.obj

[754/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/split_argv.c.obj

[755/912] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/app_trace.c.obj

[756/912] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/port/port_uart.c.obj

[757/912] Building C object esp-idf/cmock/CMakeFiles/__idf_cmock.dir/CMock/src/cmock.c.obj

[758/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_cmd.c.obj

[759/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_dbl.c.obj

[760/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_hashtable.c.obj

[761/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_dstr.c.obj

[762/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/commands.c.obj

[763/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_file.c.obj

[764/912] Linking C static library esp-idf\mbedtls\libmbedtls.a

[765/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/esp_console_repl.c.obj

[766/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/linenoise/linenoise.c.obj

[767/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_int.c.obj

[768/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_end.c.obj

[769/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_rem.c.obj

[770/912] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/uart/uart.c.obj

[771/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_lit.c.obj

[772/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_date.c.obj

[773/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_str.c.obj

[774/912] Linking C static library esp-idf\esp_pm\libesp_pm.a

[775/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_rex.c.obj

[776/912] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_freertos.c.obj

[777/912] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_port_esp32.c.obj

[778/912] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/port/esp/unity_utils_memory_esp.c.obj

[779/912] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_memory.c.obj

[780/912] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_runner.c.obj

[781/912] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity/src/unity.c.obj

[782/912] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_cache.c.obj

[783/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_utils.c.obj

[784/912] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hid_common.c.obj

[785/912] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hidd.c.obj

[786/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/src/ffunicode.c.obj

[787/912] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/argtable3.c.obj

[788/912] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hidh.c.obj

[789/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_ops.c.obj

[790/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_io.c.obj

[791/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_common.c.obj

[792/912] Linking C static library esp-idf\driver\libdriver.a

[793/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_st7789.c.obj

[794/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_io_i2c.c.obj

[795/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/constants.pb-c.c.obj

[796/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat_spiflash.c.obj

[797/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat_sdmmc.c.obj

[798/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_ssd1306.c.obj

[799/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_nt35510.c.obj

[800/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec1.pb-c.c.obj

[801/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec0.pb-c.c.obj

[802/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat.c.obj

[803/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_io_spi.c.obj

[804/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_io_i80.c.obj

[805/912] Linking C static library esp-idf\efuse\libefuse.a

[806/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/session.pb-c.c.obj

[807/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec2.pb-c.c.obj

[808/912] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_rgb.c.obj

[809/912] Building C object esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/protobuf-c/protobuf-c/protobuf-c.c.obj

[810/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security0.c.obj

[811/912] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/proto-c/esp_local_ctrl.pb-c.c.obj

[812/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/crypto/srp6a/esp_srp_mpi.c.obj

[813/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/common/protocomm.c.obj

[814/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/transports/protocomm_httpd.c.obj

[815/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/crypto/srp6a/esp_srp.c.obj

[816/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/transports/protocomm_console.c.obj

[817/912] Linking C static library esp-idf\esp_ringbuf\libesp_ringbuf.a

[818/912] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl_transport_httpd.c.obj

[819/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security2.c.obj

[820/912] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl_handler.c.obj

[821/912] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security1.c.obj

[822/912] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl.c.obj

[823/912] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_checksum.c.obj

[824/912] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_binary.c.obj

[825/912] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_common.c.obj

[826/912] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/Partition.cpp.obj

[827/912] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/port/xtensa/core_dump_port.c.obj

[828/912] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/SPI_Flash.cpp.obj

[829/912] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_elf.c.obj

[830/912] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_flash.c.obj

[831/912] Linking C static library esp-idf\xtensa\libxtensa.a

[832/912] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_uart.c.obj

[833/912] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Ext_Perf.cpp.obj

[834/912] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/wear_levelling.cpp.obj

[835/912] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Ext_Safe.cpp.obj

[836/912] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_init.c.obj

[837/912] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Flash.cpp.obj

[838/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_rawflash.c.obj

[839/912] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_io.c.obj

[840/912] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_common.c.obj

[841/912] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_cmd.c.obj

[842/912] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_sd.c.obj

[843/912] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_mmc.c.obj

[844/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/port/freertos/ffsystem.c.obj

[845/912] Linking C static library esp-idf\esp_hid\libesp_hid.a

[846/912] Linking C static library esp-idf\app_trace\libapp_trace.a

[847/912] Linking C static library esp-idf\unity\libunity.a

[848/912] Linking C static library esp-idf\console\libconsole.a

[849/912] Linking C static library esp-idf\esp_lcd\libesp_lcd.a

[850/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_sdmmc.c.obj

[851/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio.c.obj

[852/912] Linking C static library esp-idf\protobuf-c\libprotobuf-c.a

[853/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_wl.c.obj

[854/912] Linking C static library esp-idf\espcoredump\libespcoredump.a

[855/912] Linking C static library esp-idf\wear_levelling\libwear_levelling.a

[856/912] Linking C static library esp-idf\sdmmc\libsdmmc.a

[857/912] Linking C static library esp-idf\cmock\libcmock.a

[858/912] Building C object esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/xtensa_perfmon_access.c.obj

[859/912] Building C object esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/xtensa_perfmon_apis.c.obj

[860/912] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/mqtt_outbox.c.obj

[861/912] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/platform_esp32_idf.c.obj

[862/912] Linking C static library esp-idf\protocomm\libprotocomm.a

[863/912] Building C object esp-idf/json/CMakeFiles/__idf_json.dir/cJSON/cJSON_Utils.c.obj

[864/912] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs_api.c.obj

[865/912] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_cache.c.obj

[866/912] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_gc.c.obj

[867/912] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_check.c.obj

[868/912] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/src/ff.c.obj

[869/912] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/mqtt_msg.c.obj

[870/912] Building C object esp-idf/json/CMakeFiles/__idf_json.dir/cJSON/cJSON.c.obj

[871/912] Linking C static library esp-idf\esp_local_ctrl\libesp_local_ctrl.a

[872/912] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/esp_spiffs.c.obj

[873/912] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_hydrogen.c.obj

[874/912] Linking C static library esp-idf\perfmon\libperfmon.a

[875/912] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_nucleus.c.obj

[876/912] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_private.c.obj

[877/912] Linking C static library esp-idf\fatfs\libfatfs.a

[878/912] Linking C static library esp-idf\json\libjson.a

[879/912] Building C object esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/touch_button.c.obj

[880/912] Building C object esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/touch_slider.c.obj

[881/912] Building C object esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/touch_matrix.c.obj

[882/912] Building C object esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/touch_element.c.obj

[883/912] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/mqtt_client.c.obj

[884/912] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_helpers.c.obj

[885/912] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/hub.c.obj

[886/912] Linking C static library esp-idf\spiffs\libspiffs.a

[887/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_config.c.obj

[888/912] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usbh.c.obj

[889/912] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_host.c.obj

[890/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_scan.pb-c.c.obj

[891/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_config.pb-c.c.obj

[892/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_scan.c.obj

[893/912] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_phy.c.obj

[894/912] Linking C static library esp-idf\mqtt\libmqtt.a

[895/912] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/hcd_dwc.c.obj

[896/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_ctrl.c.obj

[897/912] Linking C static library esp-idf\touch_element\libtouch_element.a

[898/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/scheme_console.c.obj

[899/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_ctrl.pb-c.c.obj

[900/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_constants.pb-c.c.obj

[901/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/scheme_softap.c.obj

[902/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/handlers.c.obj

[903/912] Linking C static library esp-idf\usb\libusb.a

[904/912] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/manager.c.obj

[905/912] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/tusb_serial_device_main.c.obj

FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/tusb_serial_device_main.c.obj 

D:\vscode\vsc-lvgl\v5.0.6\esp-idf\tools\xtensa-esp32s3-elf\esp-12.2.0_20230208\xtensa-esp32s3-elf\bin\xtensa-esp32s3-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.1.2\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DUNITY_INCLUDE_CONFIG_H -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -ID:/vscode/projects-lvgl/tusb_serial_device/build/config -ID:/vscode/projects-lvgl/tusb_serial_device/main -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/freertos/FreeRTOS-Kernel/include -ID:/esp/v5.1.2/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/freertos/esp_additions/include/freertos -ID:/esp/v5.1.2/esp-idf/components/freertos/esp_additions/include -ID:/esp/v5.1.2/esp-idf/components/freertos/esp_additions/arch/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/heap/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_system/include -ID:/esp/v5.1.2/esp-idf/components/esp_system/port/soc -ID:/esp/v5.1.2/esp-idf/components/esp_system/port/include/private -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/lwip/include -ID:/esp/v5.1.2/esp-idf/components/lwip/include/apps -ID:/esp/v5.1.2/esp-idf/components/lwip/include/apps/sntp -ID:/esp/v5.1.2/esp-idf/components/lwip/lwip/src/include -ID:/esp/v5.1.2/esp-idf/components/lwip/port/include -ID:/esp/v5.1.2/esp-idf/components/lwip/port/freertos/include -ID:/esp/v5.1.2/esp-idf/components/lwip/port/esp32xx/include -ID:/esp/v5.1.2/esp-idf/components/lwip/port/esp32xx/include/arch -ID:/esp/v5.1.2/esp-idf/components/esp_ringbuf/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/driver/include -ID:/esp/v5.1.2/esp-idf/components/driver/deprecated -ID:/esp/v5.1.2/esp-idf/components/driver/analog_comparator/include -ID:/esp/v5.1.2/esp-idf/components/driver/dac/include -ID:/esp/v5.1.2/esp-idf/components/driver/gpio/include -ID:/esp/v5.1.2/esp-idf/components/driver/gptimer/include -ID:/esp/v5.1.2/esp-idf/components/driver/i2c/include -ID:/esp/v5.1.2/esp-idf/components/driver/i2s/include -ID:/esp/v5.1.2/esp-idf/components/driver/ledc/include -ID:/esp/v5.1.2/esp-idf/components/driver/mcpwm/include -ID:/esp/v5.1.2/esp-idf/components/driver/parlio/include -ID:/esp/v5.1.2/esp-idf/components/driver/pcnt/include -ID:/esp/v5.1.2/esp-idf/components/driver/rmt/include -ID:/esp/v5.1.2/esp-idf/components/driver/sdio_slave/include -ID:/esp/v5.1.2/esp-idf/components/driver/sdmmc/include -ID:/esp/v5.1.2/esp-idf/components/driver/sigma_delta/include -ID:/esp/v5.1.2/esp-idf/components/driver/spi/include -ID:/esp/v5.1.2/esp-idf/components/driver/temperature_sensor/include -ID:/esp/v5.1.2/esp-idf/components/driver/touch_sensor/include -ID:/esp/v5.1.2/esp-idf/components/driver/twai/include -ID:/esp/v5.1.2/esp-idf/components/driver/uart/include -ID:/esp/v5.1.2/esp-idf/components/driver/usb_serial_jtag/include -ID:/esp/v5.1.2/esp-idf/components/driver/touch_sensor/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_pm/include -ID:/esp/v5.1.2/esp-idf/components/mbedtls/port/include -ID:/esp/v5.1.2/esp-idf/components/mbedtls/mbedtls/include -ID:/esp/v5.1.2/esp-idf/components/mbedtls/mbedtls/library -ID:/esp/v5.1.2/esp-idf/components/mbedtls/esp_crt_bundle/include -ID:/esp/v5.1.2/esp-idf/components/mbedtls/mbedtls/3rdparty/everest/include -ID:/esp/v5.1.2/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m -ID:/esp/v5.1.2/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/esp_partition/include -ID:/esp/v5.1.2/esp-idf/components/app_update/include -ID:/esp/v5.1.2/esp-idf/components/esp_mm/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/pthread/include -ID:/esp/v5.1.2/esp-idf/components/esp_timer/include -ID:/esp/v5.1.2/esp-idf/components/app_trace/include -ID:/esp/v5.1.2/esp-idf/components/esp_event/include -ID:/esp/v5.1.2/esp-idf/components/nvs_flash/include -ID:/esp/v5.1.2/esp-idf/components/esp_phy/include -ID:/esp/v5.1.2/esp-idf/components/esp_phy/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/vfs/include -ID:/esp/v5.1.2/esp-idf/components/esp_netif/include -ID:/esp/v5.1.2/esp-idf/components/wpa_supplicant/include -ID:/esp/v5.1.2/esp-idf/components/wpa_supplicant/port/include -ID:/esp/v5.1.2/esp-idf/components/wpa_supplicant/esp_supplicant/include -ID:/esp/v5.1.2/esp-idf/components/esp_coex/include -ID:/esp/v5.1.2/esp-idf/components/esp_wifi/include -ID:/esp/v5.1.2/esp-idf/components/esp_wifi/wifi_apps/include -ID:/esp/v5.1.2/esp-idf/components/unity/include -ID:/esp/v5.1.2/esp-idf/components/unity/unity/src -ID:/esp/v5.1.2/esp-idf/components/cmock/CMock/src -ID:/esp/v5.1.2/esp-idf/components/console -ID:/esp/v5.1.2/esp-idf/components/http_parser -ID:/esp/v5.1.2/esp-idf/components/esp-tls -ID:/esp/v5.1.2/esp-idf/components/esp-tls/esp-tls-crypto -ID:/esp/v5.1.2/esp-idf/components/esp_adc/include -ID:/esp/v5.1.2/esp-idf/components/esp_adc/interface -ID:/esp/v5.1.2/esp-idf/components/esp_adc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_adc/deprecated/include -ID:/esp/v5.1.2/esp-idf/components/esp_eth/include -ID:/esp/v5.1.2/esp-idf/components/esp_gdbstub/include -ID:/esp/v5.1.2/esp-idf/components/esp_hid/include -ID:/esp/v5.1.2/esp-idf/components/tcp_transport/include -ID:/esp/v5.1.2/esp-idf/components/esp_http_client/include -ID:/esp/v5.1.2/esp-idf/components/esp_http_server/include -ID:/esp/v5.1.2/esp-idf/components/esp_https_ota/include -ID:/esp/v5.1.2/esp-idf/components/esp_psram/include -ID:/esp/v5.1.2/esp-idf/components/esp_lcd/include -ID:/esp/v5.1.2/esp-idf/components/esp_lcd/interface -ID:/esp/v5.1.2/esp-idf/components/protobuf-c/protobuf-c -ID:/esp/v5.1.2/esp-idf/components/protocomm/include/common -ID:/esp/v5.1.2/esp-idf/components/protocomm/include/security -ID:/esp/v5.1.2/esp-idf/components/protocomm/include/transports -ID:/esp/v5.1.2/esp-idf/components/esp_local_ctrl/include -ID:/esp/v5.1.2/esp-idf/components/espcoredump/include -ID:/esp/v5.1.2/esp-idf/components/espcoredump/include/port/xtensa -ID:/esp/v5.1.2/esp-idf/components/wear_levelling/include -ID:/esp/v5.1.2/esp-idf/components/sdmmc/include -ID:/esp/v5.1.2/esp-idf/components/fatfs/diskio -ID:/esp/v5.1.2/esp-idf/components/fatfs/vfs -ID:/esp/v5.1.2/esp-idf/components/fatfs/src -ID:/esp/v5.1.2/esp-idf/components/idf_test/include -ID:/esp/v5.1.2/esp-idf/components/idf_test/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/ieee802154/include -ID:/esp/v5.1.2/esp-idf/components/json/cJSON -ID:/esp/v5.1.2/esp-idf/components/mqtt/esp-mqtt/include -ID:/esp/v5.1.2/esp-idf/components/perfmon/include -ID:/esp/v5.1.2/esp-idf/components/spiffs/include -ID:/esp/v5.1.2/esp-idf/components/touch_element/include -ID:/esp/v5.1.2/esp-idf/components/ulp/ulp_common/include -ID:/esp/v5.1.2/esp-idf/components/ulp/ulp_common/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/usb/include -ID:/esp/v5.1.2/esp-idf/components/wifi_provisioning/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fmacro-prefix-map=D:/vscode/projects-lvgl/tusb_serial_device=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -DconfigENABLE_FREERTOS_DEBUG_OCDAWARE=1 -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/tusb_serial_device_main.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\tusb_serial_device_main.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/tusb_serial_device_main.c.obj -c D:/vscode/projects-lvgl/tusb_serial_device/main/tusb_serial_device_main.c

D:/vscode/projects-lvgl/tusb_serial_device/main/tusb_serial_device_main.c:278:55: error: unknown type name 'cdcacm_event_t'; did you mean 'uart_event_t'?
  278 | void tinyusb_cdc_line_state_changed_callback(int itf, cdcacm_event_t *event)
      |                                                       ^~~~~~~~~~~~~~
      |                                                       uart_event_t
D:/vscode/projects-lvgl/tusb_serial_device/main/tusb_serial_device_main.c:57:16: warning: 'hlk_rx_buffer' defined but not used [-Wunused-variable]
   57 | static uint8_t hlk_rx_buffer[HLK_RESPONSE_MAX_SIZE];
      |                ^~~~~~~~~~~~~
D:/vscode/projects-lvgl/tusb_serial_device/main/tusb_serial_device_main.c:56:16: warning: 'hlk_tx_buffer' defined but not used [-Wunused-variable]
   56 | static uint8_t hlk_tx_buffer[HLK_CMD_MAX_SIZE];
      |                ^~~~~~~~~~~~~
D:/vscode/projects-lvgl/tusb_serial_device/main/tusb_serial_device_main.c:55:16: warning: 'pc_rx_buffer' defined but not used [-Wunused-variable]
   55 | static uint8_t pc_rx_buffer[PC_UART_BUF_SIZE];
      |                ^~~~~~~~~~~~
[906/912] Linking C static library esp-idf\wifi_provisioning\libwifi_provisioning.a

ninja: build stopped: subcommand failed.

