# ninja log v5
4	214	7716447800395422	project_elf_src_esp32s3.c	3cfc1ccdf6f2b8d5
4	214	7716447800395422	D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader/project_elf_src_esp32s3.c	3cfc1ccdf6f2b8d5
16	341	7716447802895798	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	bfa10ccb2f550936
52	356	7716447802858220	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	8ab797ad793a15c
62	369	7716447802700059	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	f40f7926f657566f
41	382	7716447802915885	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	60dd8d6bb89322fe
100	395	7716447802737658	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	5f0dfd51062242ff
140	408	7716447803255215	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	b9070af0b44eda56
24	421	7716447803036503	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	a67bec207fe7248b
152	434	7716447803335610	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	860e3f81f28207b3
32	446	7716447803255215	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	3479c9dd71aa28d
113	460	7716447803433600	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	29b0bdd505aa5fb8
88	473	7716447803591794	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	4d916e3f57b83dee
73	486	7716447803750016	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	2f358572ea9dfc07
215	500	7716447803948382	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	b6bbd3f4a680ea7d
175	521	7716447804322500	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	eb3a36d9c3d257e
164	534	7716447804224618	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	cee309e0af03306c
127	547	7716447804322500	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	12bd0a99c12f6aec
187	561	7716447804304986	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	b1e52ee891845d70
198	574	7716447804881504	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	de2c61cc82120137
357	587	7716447805035811	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	d8d4b5d858d4ed44
383	600	7716447805258821	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	74159f528319cc5e
343	667	7716447805884186	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	b487eeaca7f92614
409	737	7716447806586209	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	a55a53a53f160122
461	768	7716447807109954	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	a75164544cbc4818
474	781	7716447807170859	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_otg_periph.c.obj	8f46b39ddd84da0d
371	817	7716447806968849	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	860a44405aebbb84
422	879	7716447808288593	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	792387ea72a81b2a
396	898	7716447808090150	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	f095585635b4cc4a
487	916	7716447808529344	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj	f281b47218104508
447	929	7716447808429262	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	529473c82f1abd4e
522	943	7716447808667456	esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj	b11409a81341b78a
509	1053	7716447810089928	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	ee4be1af2625d462
601	1076	7716447810278420	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	d199ef5b0cf227e3
562	1193	7716447811201547	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	396ccaa51ebf02eb
575	1206	7716447811296984	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	6af7b0f6309c8e42
548	1230	7716447811135650	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	e9583725b5b0d617
535	1243	7716447810238213	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	3b95900f3a467558
738	1320	7716447812563700	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	e43cc2a67f664651
768	1335	7716447812731030	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	f719029b973bbb26
818	1375	7716447813015329	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	dd9c9e70bc67f083
588	1391	7716447813344769	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	7dc9993402a3eb7e
782	1405	7716447813405016	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	40db608353d0af28
668	1495	7716447814366162	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	96833a716ceabfc0
899	1509	7716447814305935	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	20064cf8e2af9ea0
880	1570	7716447815117037	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	95f81be4c8fc014d
1207	1661	7716447816072231	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	ab1d00ae5bfc25eb
944	1731	7716447816388706	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	646615f5285034f1
1244	1745	7716447816782951	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	feb4287f3306fc9
917	1759	7716447816782951	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	8ab77cab5fefcd43
435	1800	7716447817340650	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	5447c991a7a9010e
1336	1829	7716447817734893	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	26dc4c44fe0b8471
1194	1843	7716447817576670	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	3b3676e7416a9f48
1379	1857	7716447817795162	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	7fdb6923c274582a
1054	1888	7716447818269832	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	8de467be7e9c3984
1078	1921	7716447818664097	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	66a573110b322052
931	1950	7716447818505892	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	48c56e2a13320a10
1393	1963	7716447818566195	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	1c85142bd4b3f2be
1496	1977	7716447819060990	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	ff648788dd6bd76d
1230	1991	7716447819214288	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	4f1369b42670c142
1406	2040	7716447819869720	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	13da65b04553adc1
1663	2057	7716447820068170	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	fb9c93268039d91c
1571	2087	7716447820146062	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	d4a4cff6d960dd06
1510	2122	7716447820640339	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	2d81ce369e9b48a1
1746	2139	7716447820645552	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	ec1c281d78e64cbe
1857	2270	7716447822198458	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	c01a70f06122faf3
1922	2283	7716447822198458	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	816e2949cebd3e50
1830	2338	7716447822547728	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	a4d9f9472cf7e137
1760	2372	7716447822942026	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	1766d949a55ce25e
1951	2451	7716447823318879	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	f36b9e8ce7d7f57
1732	2468	7716447823951920	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	1b06584838ef0d0c
1801	2482	7716447823675710	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	5dfd012ff666284
2074	2496	7716447823931823	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	693d09bf87e0eaee
2088	2510	7716447823733395	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	42ff052d5081384a
1964	2552	7716447824781060	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	a923cc71694403e
1322	2568	7716447824482131	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	534ddc36f0000adb
1844	2581	7716447825185515	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	4a2f251a892efbe7
1889	2631	7716447825574876	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	9f7f806bafa68a10
1979	2651	7716447825989255	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	5381d7bd99d21d9d
2123	2653	7716447825908861	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	8a566de351e34845
1992	2707	7716447825851120	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	5f67bfae7e0cf304
2373	2729	7716447826798117	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	ada535ca7bc8e26
2271	2747	7716447826978110	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	681feaabfec87475
2497	2749	7716447826874236	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	40cc6ef66d67f00e
2140	2755	7716447827102788	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	65b017d012e3e6d5
2041	2770	7716447827176692	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ea7088ada182e032
2340	2781	7716447827385791	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	a586072e82a3b828
2284	2788	7716447827446060	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	7b43162a80edb56a
2511	2805	7716447827492582	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	738d17023bfa57c5
2470	2807	7716447827532753	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	b11622fc8cc4bad2
2483	2810	7716447827648305	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	66dfca9912e3b0d
2553	2818	7716447827806499	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	af673616e90af75b
2452	2837	7716447827906973	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	7a6af4b9698455c5
2570	2882	7716447828457222	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	43e243b27d8d2737
2789	2953	7716447829144784	esp-idf/log/liblog.a	58b4aea6409b00db
2954	3106	7716447830686112	esp-idf/esp_rom/libesp_rom.a	b99ed6879741c397
3107	3227	7716447831855019	esp-idf/esp_common/libesp_common.a	52337d4e85515ed
3227	3364	7716447833295638	esp-idf/esp_hw_support/libesp_hw_support.a	e976306cd24a7f5c
3365	3489	7716447834450918	esp-idf/esp_system/libesp_system.a	d7f4a1a7875a384
3490	3617	7716447835791041	esp-idf/efuse/libefuse.a	b17ec91bc3d039a0
3837	4383	7716447842271459	esp-idf/bootloader_support/libbootloader_support.a	3ed446d8a14c9543
4395	5267	7716447851641482	esp-idf/esp_app_format/libesp_app_format.a	c8af1323a9c2ee97
5279	5888	7716447856847003	esp-idf/spi_flash/libspi_flash.a	b0cf5fb734690fb
5889	6377	7716447862977733	esp-idf/hal/libhal.a	3bd3223175fcd0fc
6377	6967	7716447868496616	esp-idf/micro-ecc/libmicro-ecc.a	212d65f992f306e
6976	7737	7716447876545037	esp-idf/soc/libsoc.a	8d92bed5f9c9e31c
7738	8330	7716447882157717	esp-idf/xtensa/libxtensa.a	89326e363232dba5
8331	8790	7716447886987158	esp-idf/main/libmain.a	8d1e8d9c6bcfe417
8791	10371	7716447902501958	bootloader.elf	afa7d5331441f64b
10372	13120	7716447929889352	.bin_timestamp	f20264e8732bcd72
10372	13120	7716447929889352	D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader/.bin_timestamp	f20264e8732bcd72
13144	13797	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8dcdfcaa23ad7e42
13144	13797	0	D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8dcdfcaa23ad7e42
6	113	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8dcdfcaa23ad7e42
6	113	0	D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8dcdfcaa23ad7e42
