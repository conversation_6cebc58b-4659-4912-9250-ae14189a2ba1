[1/5] cmd.exe /C "cd /D D:\vscode\projects-lvgl\tusb_serial_device\build\esp-idf\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe D:/esp/v5.1.2/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app D:/vscode/projects-lvgl/tusb_serial_device/build/partition_table/partition-table.bin D:/vscode/projects-lvgl/tusb_serial_device/build/tusb_serial_device.bin"

tusb_serial_device.bin binary size 0x42760 bytes. Smallest app partition is 0x100000 bytes. 0xbd8a0 bytes (74%) free.

[2/5] Performing build step for 'bootloader'

[1/1] cmd.exe /C "cd /D D:\vscode\projects-lvgl\tusb_serial_device\build\bootloader\esp-idf\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe D:/esp/v5.1.2/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader/bootloader.bin"

Bootloader binary size 0x51c0 bytes. 0x2e40 bytes (36%) free.

[2/3] cmd.exe /C "cd /D D:\esp\v5.1.2\esp-idf\components\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\tools\cmake\3.24.0\bin\cmake.exe -D IDF_PATH=D:/esp/v5.1.2/esp-idf -D SERIAL_TOOL=D:/vscode/vsc-lvgl/v5.0.6/esp-idf/python_env/idf5.1_py3.11_env/Scripts/python.exe;;D:/esp/v5.1.2/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=D:/vscode/projects-lvgl/tusb_serial_device/build -P D:/esp/v5.1.2/esp-idf/components/esptool_py/run_serial_tool.cmake"

esptool.py --chip esp32s3 -p COM6 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 2MB 0x0 bootloader/bootloader.bin 0x10000 tusb_serial_device.bin 0x8000 partition_table/partition-table.bin

esptool.py v4.8.1

Serial port COM6

Connecting....

Chip is ESP32-S3 (QFN56) (revision v0.2)

Features: WiFi, BLE, Embedded PSRAM 8MB (AP_3v3)

Crystal is 40MHz

MAC: 48:ca:43:30:9a:38

Uploading stub...

Running stub...

Stub running...

Changing baud rate to 460800

Changed.

Configuring flash size...

Flash will be erased from 0x00000000 to 0x00005fff...

Flash will be erased from 0x00010000 to 0x00052fff...

Flash will be erased from 0x00008000 to 0x00008fff...

SHA digest in image updated

Compressed 20928 bytes to 13291...

Writing at 0x00000000... (100 %)

Wrote 20928 bytes (13291 compressed) at 0x00000000 in 0.6 seconds (effective 286.2 kbit/s)...

Hash of data verified.

Compressed 272224 bytes to 145135...

Writing at 0x00010000... (11 %)

Writing at 0x0001cc01... (22 %)

Writing at 0x0002497e... (33 %)

Writing at 0x0002add2... (44 %)

Writing at 0x00030da2... (55 %)

Writing at 0x00036ec8... (66 %)

Writing at 0x0004040e... (77 %)

Writing at 0x00047568... (88 %)

Writing at 0x0004d145... (100 %)

Wrote 272224 bytes (145135 compressed) at 0x00010000 in 3.4 seconds (effective 640.6 kbit/s)...

Hash of data verified.

Compressed 3072 bytes to 103...

Writing at 0x00008000... (100 %)

Wrote 3072 bytes (103 compressed) at 0x00008000 in 0.0 seconds (effective 496.8 kbit/s)...

Hash of data verified.



Leaving...

Hard resetting via RTS pin...

