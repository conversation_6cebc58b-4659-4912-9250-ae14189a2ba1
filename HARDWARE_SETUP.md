# Hardware Setup Guide for ESP32-S3 to HLK-TX510 Communication

## Hardware Connections

### ESP32-S3 WaveShare Touch LCD 4.3" Board

**USB Connections:**
- **UART Port (USB-C)**: Connect to PC for programming and debugging
- **USB Port (USB-C)**: Will appear as USB CDC device for sending commands

**UART Pins for HLK-TX510:**
- **GPIO17 (TX)**: Connect to HLK-TX510 RX pin
- **GPIO18 (RX)**: Connect to HLK-TX510 TX pin
- **GND**: Connect to HLK-TX510 GND
- **3.3V or 5V**: Connect to HLK-TX510 VCC (check HLK-TX510 voltage requirements)

### HLK-TX510 Facial Recognition Module

**UART Pins:**
- **TX**: Connect to ESP32-S3 GPIO18 (RX)
- **RX**: Connect to ESP32-S3 GPIO17 (TX)
- **GND**: Connect to ESP32-S3 GND
- **VCC**: Connect to ESP32-S3 3.3V or 5V power

## Testing Steps

### 1. Flash the Firmware

```powershell
# Flash to default COM port (COM3)
.\flash.ps1

# Or specify a different port
.\flash.ps1 -Port COM4
```

### 2. Monitor Debug Output

Open a separate terminal and run:
```powershell
# Monitor default port
.\monitor.ps1

# Or specify a different port
.\monitor.ps1 -Port COM4
```

### 3. Test Communication

#### Option A: Using Python Test Script
```bash
# Interactive mode
python test_communication.py

# Single command test
python test_communication.py -c "010203040506070809"

# Specify port
python test_communication.py -p COM5
```

#### Option B: Using Serial Terminal
1. Open any serial terminal (PuTTY, Arduino Serial Monitor, etc.)
2. Connect to the ESP32-S3 USB CDC port (usually appears as a new COM port)
3. Set baud rate to 115200
4. Send 18-character hex strings (e.g., "010203040506070809")

## Expected Behavior

### Without HLK-TX510 Connected:
- ESP32-S3 should boot and show initialization messages
- USB CDC should accept commands but show "No response from HLK-TX510" or timeout

### With HLK-TX510 Connected:
- ESP32-S3 should boot and initialize UART
- Commands sent via USB CDC should be forwarded to HLK-TX510
- Responses from HLK-TX510 should be returned via USB CDC

## Troubleshooting

### ESP32-S3 Not Detected:
1. Check USB cable (data cable, not charge-only)
2. Try different USB port
3. Install ESP32-S3 drivers if needed
4. Check Device Manager for new COM ports

### No Response from HLK-TX510:
1. Verify wiring connections
2. Check HLK-TX510 power supply
3. Verify HLK-TX510 baud rate (should be 115200)
4. Check monitor output for UART errors

### Build/Flash Issues:
1. Run `.\build.ps1` to rebuild
2. Check ESP-IDF environment setup
3. Verify COM port in Device Manager

## Debug Information

The ESP32-S3 will output debug information via the UART port (programming port). 
Use `.\monitor.ps1` to see:
- Initialization status
- UART setup confirmation
- Command/response logging
- Error messages
