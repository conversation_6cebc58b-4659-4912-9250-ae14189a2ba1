#!/usr/bin/env python3
"""
Test script for ESP32-S3 to HLK-TX510 Communication Bridge
This script sends test commands via USB CDC and displays responses
"""

import serial
import time
import sys
import argparse

def find_esp32_port():
    """Try to find the ESP32-S3 USB CDC port automatically"""
    import serial.tools.list_ports
    
    ports = serial.tools.list_ports.comports()
    for port in ports:
        # Look for ESP32-S3 or USB Serial Device
        if any(keyword in port.description.lower() for keyword in ['esp32', 'usb serial', 'cdc']):
            return port.device
    return None

def send_hex_command(ser, hex_command):
    """Send a hex command and wait for response"""
    print(f"Sending: {hex_command}")
    
    # Send command
    ser.write(hex_command.encode() + b'\r\n')
    ser.flush()
    
    # Wait for response
    time.sleep(1)
    
    # Read response
    response = b""
    while ser.in_waiting > 0:
        response += ser.read(ser.in_waiting)
        time.sleep(0.1)
    
    if response:
        print(f"Received: {response.decode('utf-8', errors='ignore').strip()}")
    else:
        print("No response received")
    
    return response

def main():
    parser = argparse.ArgumentParser(description='Test ESP32-S3 to HLK-TX510 communication')
    parser.add_argument('--port', '-p', help='COM port (e.g., COM4)')
    parser.add_argument('--baudrate', '-b', type=int, default=115200, help='Baud rate (default: 115200)')
    parser.add_argument('--command', '-c', help='Single hex command to send (18 chars)')
    
    args = parser.parse_args()
    
    # Find port
    if args.port:
        port = args.port
    else:
        port = find_esp32_port()
        if not port:
            print("Could not find ESP32-S3 port automatically. Please specify with --port")
            sys.exit(1)
        print(f"Auto-detected port: {port}")
    
    try:
        # Open serial connection
        print(f"Connecting to {port} at {args.baudrate} baud...")
        ser = serial.Serial(port, args.baudrate, timeout=2)
        time.sleep(2)  # Wait for connection to stabilize
        
        print("Connected! ESP32-S3 HLK-TX510 Communication Bridge Test")
        print("=" * 60)
        
        if args.command:
            # Send single command
            if len(args.command) != 18:
                print("Error: Command must be exactly 18 hex characters (9 bytes)")
                sys.exit(1)
            send_hex_command(ser, args.command)
        else:
            # Interactive mode
            print("Interactive mode - Enter hex commands (18 characters) or 'quit' to exit")
            print("Example commands:")
            print("  010203040506070809  - Test command")
            print("  AABBCCDDEEFF001122  - Another test")
            print()
            
            while True:
                try:
                    command = input("Enter hex command (18 chars): ").strip().upper()
                    
                    if command.lower() in ['quit', 'exit', 'q']:
                        break
                    
                    if len(command) != 18:
                        print("Error: Command must be exactly 18 hex characters")
                        continue
                    
                    # Validate hex
                    try:
                        int(command, 16)
                    except ValueError:
                        print("Error: Invalid hex characters")
                        continue
                    
                    send_hex_command(ser, command)
                    print()
                    
                except KeyboardInterrupt:
                    break
        
        ser.close()
        print("\nConnection closed.")
        
    except serial.SerialException as e:
        print(f"Serial error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
