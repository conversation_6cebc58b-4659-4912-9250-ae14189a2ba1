# ESP-IDF Flash Script for ESP32-S3 HLK-TX510 Communication Bridge
# This script flashes the firmware to the ESP32-S3 board

param(
    [string]$Port = "COM3"  # Default COM port, change as needed
)

Write-Host "Flashing ESP32-S3 HLK-TX510 Communication Bridge..." -ForegroundColor Green

# Set ESP-IDF path
$env:IDF_PATH = "D:\esp\v5.1.2\esp-idf"

# Set Python environment path
$PYTHON_ENV = "D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe"

# Check if build exists
if (-not (Test-Path "build\tusb_serial_device.bin")) {
    Write-Host "Build not found. Building first..." -ForegroundColor Yellow
    .\build.ps1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed. Cannot flash." -ForegroundColor Red
        exit 1
    }
}

# Flash the firmware
Write-Host "Flashing to port $Port..." -ForegroundColor Green
& $PYTHON_ENV "D:\esp\v5.1.2\esp-idf\tools\idf.py" -p $Port flash

if ($LASTEXITCODE -eq 0) {
    Write-Host "Flash completed successfully!" -ForegroundColor Green
    Write-Host "You can now test the communication bridge." -ForegroundColor Cyan
    Write-Host "Connect to the USB CDC port to send hex commands." -ForegroundColor Cyan
} else {
    Write-Host "Flash failed!" -ForegroundColor Red
    exit 1
}
