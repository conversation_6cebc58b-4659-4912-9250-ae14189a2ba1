# ESP-IDF Build Script for ESP32-S3 HLK-TX510 Communication Bridge
# This script sets up the proper ESP-IDF environment and builds the project

Write-Host "Setting up ESP-IDF environment..." -ForegroundColor Green

# Set ESP-IDF path
$env:IDF_PATH = "D:\esp\v5.1.2\esp-idf"

# Set Python environment path
$PYTHON_ENV = "D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe"

# Check if Python environment exists
if (-not (Test-Path $PYTHON_ENV)) {
    Write-Host "Python environment not found. Installing..." -ForegroundColor Yellow
    python "D:\esp\v5.1.2\esp-idf\tools\idf_tools.py" install-python-env
}

# Build the project
Write-Host "Building project..." -ForegroundColor Green
& $PYTHON_ENV "D:\esp\v5.1.2\esp-idf\tools\idf.py" build

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Host "Binary file: build\tusb_serial_device.bin" -ForegroundColor Cyan
} else {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}
