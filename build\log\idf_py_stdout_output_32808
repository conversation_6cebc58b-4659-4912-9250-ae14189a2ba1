[1/10] Performing build step for 'bootloader'

[1/1] cmd.exe /C "cd /D D:\vscode\projects-lvgl\tusb_serial_device\build\bootloader\esp-idf\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe D:/esp/v5.1.2/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/vscode/projects-lvgl/tusb_serial_device/build/bootloader/bootloader.bin"

Bootloader binary size 0x51c0 bytes. 0x2e40 bytes (36%) free.

[2/8] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/tusb_serial_device_main.c.obj

[3/8] Linking C static library esp-idf\main\libmain.a

[4/8] Generating ld/sections.ld

[5/8] Building C object CMakeFiles/tusb_serial_device.elf.dir/project_elf_src_esp32s3.c.obj

[6/8] Linking CXX executable tusb_serial_device.elf

[7/8] Generating binary image from built executable

esptool.py v4.8.1

Creating esp32s3 image...

Merged 2 ELF sections

Successfully created esp32s3 image.

Generated D:/vscode/projects-lvgl/tusb_serial_device/build/tusb_serial_device.bin
[8/8] cmd.exe /C "cd /D D:\vscode\projects-lvgl\tusb_serial_device\build\esp-idf\esptool_py && D:\vscode\vsc-lvgl\v5.0.6\esp-idf\python_env\idf5.1_py3.11_env\Scripts\python.exe D:/esp/v5.1.2/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app D:/vscode/projects-lvgl/tusb_serial_device/build/partition_table/partition-table.bin D:/vscode/projects-lvgl/tusb_serial_device/build/tusb_serial_device.bin"

tusb_serial_device.bin binary size 0x42760 bytes. Smallest app partition is 0x100000 bytes. 0xbd8a0 bytes (74%) free.

